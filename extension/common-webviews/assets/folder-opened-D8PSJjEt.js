import{S as f,i as g,s as H,D as v,d as l,a9 as L,f as p,n as a,k as h,a8 as x,Y as b,V as U,h as d,Z as k,b as u}from"./SpinnerAugment-uKUHz-bK.js";import{e as V}from"./IconButtonAugment-CQzh_Hae.js";const C=[];for(let i=0;i<256;++i)C.push((i+256).toString(16).slice(1));let m;const D=new Uint8Array(16),Z={randomUUID:typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};function I(i,e,o){var t;if(Z.randomUUID&&!e&&!i)return Z.randomUUID();const s=(i=i||{}).random??((t=i.rng)==null?void 0:t.call(i))??function(){if(!m){if(typeof crypto>"u"||!crypto.getRandomValues)throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");m=crypto.getRandomValues.bind(crypto)}return m(D)}();if(s.length<16)throw new Error("Random bytes length must be >= 16");return s[6]=15&s[6]|64,s[8]=63&s[8]|128,function(n,r=0){return(C[n[r+0]]+C[n[r+1]]+C[n[r+2]]+C[n[r+3]]+"-"+C[n[r+4]]+C[n[r+5]]+"-"+C[n[r+6]]+C[n[r+7]]+"-"+C[n[r+8]]+C[n[r+9]]+"-"+C[n[r+10]]+C[n[r+11]]+C[n[r+12]]+C[n[r+13]]+C[n[r+14]]+C[n[r+15]]).toLowerCase()}(s)}function A(){return I()}function y(i,e,o){const s=i.slice();return s[3]=e[o],s}function M(i){let e,o,s,t=i[3]+"";return{c(){e=v("span"),o=b(t),s=U(),l(e,"class","c-keyboard-shortcut-hint__icon svelte-1txw16l")},m(n,r){p(n,e,r),d(e,o),d(e,s)},p(n,r){2&r&&t!==(t=n[3]+"")&&k(o,t)},d(n){n&&h(e)}}}function B(i){let e,o,s=V(i[1]),t=[];for(let n=0;n<s.length;n+=1)t[n]=M(y(i,s,n));return{c(){e=v("span");for(let n=0;n<t.length;n+=1)t[n].c();l(e,"class",o=L(`c-keyboard-shortcut-hint ${i[0]}`)+" svelte-1txw16l")},m(n,r){p(n,e,r);for(let c=0;c<t.length;c+=1)t[c]&&t[c].m(e,null)},p(n,[r]){if(2&r){let c;for(s=V(n[1]),c=0;c<s.length;c+=1){const w=y(n,s,c);t[c]?t[c].p(w,r):(t[c]=M(w),t[c].c(),t[c].m(e,null))}for(;c<t.length;c+=1)t[c].d(1);t.length=s.length}1&r&&o!==(o=L(`c-keyboard-shortcut-hint ${n[0]}`)+" svelte-1txw16l")&&l(e,"class",o)},i:a,o:a,d(n){n&&h(e),x(t,n)}}}function R(i,e,o){let{class:s=""}=e,{keybinding:t}=e,{icons:n=(t==null?void 0:t.split("-"))??[]}=e;return i.$$set=r=>{"class"in r&&o(0,s=r.class),"keybinding"in r&&o(2,t=r.keybinding),"icons"in r&&o(1,n=r.icons)},[s,n,t]}class F extends f{constructor(e){super(),g(this,e,R,B,H,{class:0,keybinding:2,icons:1})}}function $(i){let e,o;return{c(){e=u("svg"),o=u("path"),l(o,"fill-rule","evenodd"),l(o,"clip-rule","evenodd"),l(o,"d","M5 2V1H10V2H5ZM4.75 0C4.33579 0 4 0.335786 4 0.75V1H3.5C2.67157 1 2 1.67157 2 2.5V12.5C2 13.3284 2.67157 14 3.5 14H7V13H3.5C3.22386 13 3 12.7761 3 12.5V2.5C3 2.22386 3.22386 2 3.5 2H4V2.25C4 2.66421 4.33579 3 4.75 3H10.25C10.6642 3 11 2.66421 11 2.25V2H11.5C11.7761 2 12 2.22386 12 2.5V7H13V2.5C13 1.67157 12.3284 1 11.5 1H11V0.75C11 0.335786 10.6642 0 10.25 0H4.75ZM9 8.5C9 8.77614 8.77614 9 8.5 9C8.22386 9 8 8.77614 8 8.5C8 8.22386 8.22386 8 8.5 8C8.77614 8 9 8.22386 9 8.5ZM10.5 9C10.7761 9 11 8.77614 11 8.5C11 8.22386 10.7761 8 10.5 8C10.2239 8 10 8.22386 10 8.5C10 8.77614 10.2239 9 10.5 9ZM13 8.5C13 8.77614 12.7761 9 12.5 9C12.2239 9 12 8.77614 12 8.5C12 8.22386 12.2239 8 12.5 8C12.7761 8 13 8.22386 13 8.5ZM14.5 9C14.7761 9 15 8.77614 15 8.5C15 8.22386 14.7761 8 14.5 8C14.2239 8 14 8.22386 14 8.5C14 8.77614 14.2239 9 14.5 9ZM15 10.5C15 10.7761 14.7761 11 14.5 11C14.2239 11 14 10.7761 14 10.5C14 10.2239 14.2239 10 14.5 10C14.7761 10 15 10.2239 15 10.5ZM14.5 13C14.7761 13 15 12.7761 15 12.5C15 12.2239 14.7761 12 14.5 12C14.2239 12 14 12.2239 14 12.5C14 12.7761 14.2239 13 14.5 13ZM14.5 15C14.7761 15 15 14.7761 15 14.5C15 14.2239 14.7761 14 14.5 14C14.2239 14 14 14.2239 14 14.5C14 14.7761 14.2239 15 14.5 15ZM8.5 11C8.77614 11 9 10.7761 9 10.5C9 10.2239 8.77614 10 8.5 10C8.22386 10 8 10.2239 8 10.5C8 10.7761 8.22386 11 8.5 11ZM9 12.5C9 12.7761 8.77614 13 8.5 13C8.22386 13 8 12.7761 8 12.5C8 12.2239 8.22386 12 8.5 12C8.77614 12 9 12.2239 9 12.5ZM8.5 15C8.77614 15 9 14.7761 9 14.5C9 14.2239 8.77614 14 8.5 14C8.22386 14 8 14.2239 8 14.5C8 14.7761 8.22386 15 8.5 15ZM11 14.5C11 14.7761 10.7761 15 10.5 15C10.2239 15 10 14.7761 10 14.5C10 14.2239 10.2239 14 10.5 14C10.7761 14 11 14.2239 11 14.5ZM12.5 15C12.7761 15 13 14.7761 13 14.5C13 14.2239 12.7761 14 12.5 14C12.2239 14 12 14.2239 12 14.5C12 14.7761 12.2239 15 12.5 15Z"),l(o,"fill","currentColor"),l(e,"width","15"),l(e,"height","15"),l(e,"viewBox","0 0 15 15"),l(e,"fill","none"),l(e,"xmlns","http://www.w3.org/2000/svg")},m(s,t){p(s,e,t),d(e,o)},p:a,i:a,o:a,d(s){s&&h(e)}}}class K extends f{constructor(e){super(),g(this,e,null,$,H,{})}}function S(i){let e,o;return{c(){e=u("svg"),o=u("path"),l(o,"fill-rule","evenodd"),l(o,"clip-rule","evenodd"),l(o,"d","M13.71 4.29L10.71 1.29L10 1H4L3 2V14L4 15H13L14 14V5L13.71 4.29ZM13 14H4V2H9V6H13V14ZM10 5V2L13 5H10Z"),l(o,"fill","currentColor"),l(e,"width","16"),l(e,"height","16"),l(e,"viewBox","0 0 16 16"),l(e,"fill","none"),l(e,"xmlns","http://www.w3.org/2000/svg")},m(s,t){p(s,e,t),d(e,o)},p:a,i:a,o:a,d(s){s&&h(e)}}}class Y extends f{constructor(e){super(),g(this,e,null,S,H,{})}}function E(i){let e,o;return{c(){e=u("svg"),o=u("path"),l(o,"d","M1.5 14H12.5L12.98 13.63L15.61 6.63L15.13 6H14V3.5L13.5 3H7.70996L6.84998 2.15002L6.5 2H1.5L1 2.5V13.5L1.5 14ZM2 3H6.29004L7.15002 3.84998L7.5 4H13V6H8.5L8.15002 6.15002L7.29004 7H3.5L3.03003 7.33997L2.03003 10.42L2 3ZM12.13 13H2.18994L3.85999 8H7.5L7.84998 7.84998L8.70996 7H14.5L12.13 13Z"),l(o,"fill","currentColor"),l(e,"width","16"),l(e,"height","16"),l(e,"viewBox","0 0 16 16"),l(e,"fill","none"),l(e,"xmlns","http://www.w3.org/2000/svg")},m(s,t){p(s,e,t),d(e,o)},p:a,i:a,o:a,d(s){s&&h(e)}}}class q extends f{constructor(e){super(),g(this,e,null,E,H,{})}}export{K as C,q as F,F as K,Y as a,A as c};
