import{S as U,i as Y,s as Z,J as F,D as _,V as X,d as z,P as T,a0 as g,f as P,h as W,Q as D,ae as ee,K as G,L as J,M as S,w as x,t as te,u as b,v as se,ab as ae,k as V,T as de,ac as oe,E as q,F as C,ah as ne,ai as j,G as H,a5 as I}from"./SpinnerAugment-uKUHz-bK.js";import{I as le}from"./IconButtonAugment-CQzh_Hae.js";import{f as K}from"./index-GYuo8qik.js";import{E as re}from"./ellipsis-Btdwvghx.js";const ie=(s,{onResize:t,options:e})=>{const d=new ResizeObserver(t);return d.observe(s,e),{destroy(){d.unobserve(s),d.disconnect()}}},ce=s=>({}),O=s=>({}),ue=s=>({}),Q=s=>({});function A(s){let t,e,d,u;return e=new le({props:{variant:"solid",color:"accent",size:2,radius:"full",title:"Show panel",$$slots:{default:[me]},$$scope:{ctx:s}}}),e.$on("click",s[12]),{c(){t=_("div"),q(e.$$.fragment),z(t,"class","c-drawer__hidden-indicator svelte-18f0m3o")},m(l,$){P(l,t,$),C(e,t,null),u=!0},p(l,$){const p={};16777216&$&&(p.$$scope={dirty:$,ctx:l}),e.$set(p)},i(l){u||(x(e.$$.fragment,l),l&&ne(()=>{u&&(d||(d=j(t,K,{y:0,x:0,duration:200},!0)),d.run(1))}),u=!0)},o(l){b(e.$$.fragment,l),l&&(d||(d=j(t,K,{y:0,x:0,duration:200},!1)),d.run(0)),u=!1},d(l){l&&V(t),H(e),l&&d&&d.end()}}}function me(s){let t,e;return t=new re({}),{c(){q(t.$$.fragment)},m(d,u){C(t,d,u),e=!0},i(d){e||(x(t.$$.fragment,d),e=!0)},o(d){b(t.$$.fragment,d),e=!1},d(d){H(t,d)}}}function he(s){let t,e,d,u,l,$,p,k,v,f,i,m,L;const M=s[20].left,h=F(M,s,s[24],Q),y=s[20].right,r=F(y,s,s[24],O);let n=s[0]&&s[3]&&A(s);return{c(){t=_("div"),e=_("div"),d=_("div"),h&&h.c(),u=X(),l=_("div"),$=X(),p=_("div"),r&&r.c(),k=X(),n&&n.c(),z(d,"class","c-drawer__left-content svelte-18f0m3o"),d.inert=s[7],T(d,"width","var(--augment-drawer-width)"),T(d,"min-width","var(--augment-drawer-width)"),T(d,"max-width","var(--augment-drawer-width)"),z(e,"class","c-drawer__left svelte-18f0m3o"),T(e,"--augment-drawer-width",s[8]+"px"),z(l,"aria-hidden","true"),z(l,"class","c-drawer__handle svelte-18f0m3o"),g(l,"is-locked",s[4]),z(p,"class","c-drawer__right svelte-18f0m3o"),z(t,"class",v="c-drawer "+s[2]+" svelte-18f0m3o"),g(t,"is-dragging",s[7]),g(t,"is-hidden",!s[8]),g(t,"is-column",s[4])},m(a,c){P(a,t,c),W(t,e),W(e,d),h&&h.m(d,null),s[21](e),W(t,u),W(t,l),W(t,$),W(t,p),r&&r.m(p,null),W(t,k),n&&n.m(t,null),s[22](t),i=!0,m||(L=[D(window,"mousemove",s[10]),D(window,"mouseup",s[11]),D(l,"mousedown",s[9]),D(l,"dblclick",s[12]),ee(f=ie.call(null,t,{onResize:s[23]}))],m=!0)},p(a,[c]){h&&h.p&&(!i||16777216&c)&&G(h,M,a,a[24],i?S(M,a[24],c,ue):J(a[24]),Q),(!i||128&c)&&(d.inert=a[7]),(!i||256&c)&&T(e,"--augment-drawer-width",a[8]+"px"),(!i||16&c)&&g(l,"is-locked",a[4]),r&&r.p&&(!i||16777216&c)&&G(r,y,a,a[24],i?S(y,a[24],c,ce):J(a[24]),O),a[0]&&a[3]?n?(n.p(a,c),9&c&&x(n,1)):(n=A(a),n.c(),x(n,1),n.m(t,null)):n&&(te(),b(n,1,1,()=>{n=null}),se()),(!i||4&c&&v!==(v="c-drawer "+a[2]+" svelte-18f0m3o"))&&z(t,"class",v),f&&ae(f.update)&&2&c&&f.update.call(null,{onResize:a[23]}),(!i||132&c)&&g(t,"is-dragging",a[7]),(!i||260&c)&&g(t,"is-hidden",!a[8]),(!i||20&c)&&g(t,"is-column",a[4])},i(a){i||(x(h,a),x(r,a),x(n),i=!0)},o(a){b(h,a),b(r,a),b(n),i=!1},d(a){a&&V(t),h&&h.d(a),s[21](null),r&&r.d(a),n&&n.d(),s[22](null),m=!1,de(L)}}}function fe(s,t,e){let d,u,l,$,{$$slots:p={},$$scope:k}=t,{initialWidth:v=300}=t,{expandedMinWidth:f=50}=t,{minimizedWidth:i=0}=t,{minimized:m=!1}=t,{class:L=""}=t,{showButton:M=!0}=t,{deadzone:h=0}=t,{columnLayoutThreshold:y=600}=t,{layoutMode:r}=t,n=!1,a=v,c=v,w=!1;function B(){if(u){if(r!==void 0)return e(4,w=r==="column"),void(w&&e(7,n=!1));e(4,w=u.clientWidth<y),w&&e(7,n=!1)}}return oe(B),s.$$set=o=>{"initialWidth"in o&&e(14,v=o.initialWidth),"expandedMinWidth"in o&&e(15,f=o.expandedMinWidth),"minimizedWidth"in o&&e(16,i=o.minimizedWidth),"minimized"in o&&e(0,m=o.minimized),"class"in o&&e(2,L=o.class),"showButton"in o&&e(3,M=o.showButton),"deadzone"in o&&e(17,h=o.deadzone),"columnLayoutThreshold"in o&&e(18,y=o.columnLayoutThreshold),"layoutMode"in o&&e(1,r=o.layoutMode),"$$scope"in o&&e(24,k=o.$$scope)},s.$$.update=()=>{3&s.$$.dirty&&(m?(e(1,r="row"),e(4,w=!1)):r!=="row"||m||(e(1,r=void 0),B())),18&s.$$.dirty&&r!==void 0&&(e(4,w=r==="column"),w&&e(7,n=!1)),589825&s.$$.dirty&&e(8,c=m?i:a)},[m,r,L,M,w,d,u,n,c,function(o){w||(e(7,n=!0),l=o.clientX,$=d.offsetWidth,o.preventDefault())},function(o){if(!n||!d||w)return;const N=o.clientX-l,E=u.clientWidth-200,R=$+N;R<f?R<f-h?e(0,m=!0):(e(19,a=f),e(0,m=!1)):R>E?(e(19,a=E),e(0,m=!1)):(e(19,a=R),e(0,m=!1))},function(){e(7,n=!1),e(19,a=Math.max(a,f))},function(){e(0,m=!m)},B,v,f,i,h,y,a,p,function(o){I[o?"unshift":"push"](()=>{d=o,e(5,d)})},function(o){I[o?"unshift":"push"](()=>{u=o,e(6,u)})},()=>r===void 0&&B(),k]}class ge extends U{constructor(t){super(),Y(this,t,fe,he,Z,{initialWidth:14,expandedMinWidth:15,minimizedWidth:16,minimized:0,class:2,showButton:3,deadzone:17,columnLayoutThreshold:18,layoutMode:1})}}export{ge as D,ie as r};
