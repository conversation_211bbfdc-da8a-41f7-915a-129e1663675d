import{F as g,R as c,a as l}from"./types-DDm27S8B.js";import{C as m,a as E}from"./message-broker-DdVtH9Vr.js";function P(n,e){const s=setInterval(()=>{const i=n.getTime()-Date.now();if(i<=0)return void clearInterval(s);const r=Math.floor(i/1e3),t=Math.floor(r/60),a=Math.floor(t/60),o=Math.floor(a/24);e(r<60?`${r}s`:t<60?`${t}m ${r%60}s`:a<24?`${a}h`:o<30?`${o}d`:"1mo")},1e3);return()=>clearInterval(s)}function v(n){if(n===void 0)return"neutral";switch(n){case l.agentPending:case l.agentStarting:case l.agentRunning:return"info";case l.agentIdle:return"success";case l.agentFailed:return"error";default:return"neutral"}}function M(n){if(n===void 0)return"neutral";switch(n){case c.workspaceRunning:return"info";case c.workspacePausing:case c.workspacePaused:case c.workspaceResuming:default:return"neutral"}}function k(n,e,s){if(e===c.workspaceResuming)return"Resuming";switch(n){case l.agentStarting:return"Starting";case l.agentRunning:return"Running";case l.agentIdle:return s?"Unread":"Idle";case l.agentPending:return"Pending";case l.agentFailed:return"Failed";default:return"Unknown"}}const d=n=>C(n),C=n=>{const e={};for(const t of n){const a=t.old_path||t.new_path;if(e[a])e[a].finalExists=t.new_path!=="",e[a].finalContent=t.new_path!==""?t.new_contents:"",e[a].finalPath=t.new_path||t.old_path,e[a].latestChange=t,t.change_type===g.deleted&&t.old_contents!==""&&(e[a].originalContent=t.old_contents);else{const o=t.old_path!=="";e[a]={originalExists:o,originalContent:o?t.old_contents:"",finalExists:t.new_path!=="",finalContent:t.new_path!==""?t.new_contents:"",finalPath:t.new_path||t.old_path,latestChange:t}}}const s=[];for(const[t,a]of Object.entries(e))if(a.originalExists!==a.finalExists||a.originalExists&&a.finalExists&&a.originalContent!==a.finalContent){const o={id:a.latestChange.id,old_path:a.originalExists?t:"",new_path:a.finalExists?a.finalPath:"",old_contents:a.originalContent,new_contents:a.finalContent,change_type:(i=a.originalExists,r=a.finalExists,!i&&r?g.added:i&&!r?g.deleted:g.modified)};s.push(o)}var i,r;return s},q=n=>{const e=n.flatMap(s=>s.changed_files);return d(e)},O=(n,e)=>{var i;const s=h(n,e);return((i=n[s])==null?void 0:i.exchange.request_message)??""},h=(n,e)=>{var s;return e<0||e>=n.length?-1:(s=n[e])!=null&&s.exchange.request_message?e:n.slice(0,e).findLastIndex(i=>i.exchange.request_message)},x=(n,e)=>{const s=n.slice(e+1).findIndex(i=>i.exchange.request_message);return s===-1?n.length:e+s+1},S=(n,e)=>{if(e<0||e>=n.length)return[];if(h(n,e)===-1){const r=n.flatMap(t=>t.changed_files);return d(r)}const s=((r,t)=>{const a=h(r,t);let o=x(r,t);const u=a===-1?0:a+1;return r.slice(u,o)})(n,e),i=s.flatMap(r=>r.changed_files);return d(i)},$=(n,e)=>{var o,u;const s=x(n,e),i=n.slice(e,s),r=(o=n[e].exchange.response_nodes)==null?void 0:o.find(f=>f.type===m.TOOL_USE);if(!r)return[];const t=(u=r.tool_use)==null?void 0:u.tool_use_id;if(!t)return[];const a=i.find(f=>{var _;return(_=f.exchange.request_nodes)==null?void 0:_.some(p=>{var w;return p.type===E.TOOL_RESULT&&((w=p.tool_result_node)==null?void 0:w.tool_use_id)===t})});return a?a.changed_files:[]};export{q as a,v as b,M as c,$ as d,O as e,S as f,k as g,P as s};
