import{S as q,i as H,s as N,a as x,E as W,F as A,j as O,af as P,w as d,u as m,G as X,a1 as E,I as Q,l as T,W as f,D as h,V as F,d as g,a9 as G,f as p,h as I,t as b,v as z,k as v,J as w,K as C,L as j,M as k,X as U}from"./SpinnerAugment-uKUHz-bK.js";import{B as Y}from"./IconButtonAugment-CQzh_Hae.js";const Z=s=>({}),J=s=>({}),_=s=>({}),K=s=>({});function M(s){let t,l;const c=s[10].iconLeft,o=w(c,s,s[20],K);return{c(){t=h("div"),o&&o.c(),g(t,"class","c-button--icon svelte-1u3rjsd")},m(i,a){p(i,t,a),o&&o.m(t,null),l=!0},p(i,a){o&&o.p&&(!l||1048576&a)&&C(o,c,i,i[20],l?k(c,i[20],a,_):j(i[20]),K)},i(i){l||(d(o,i),l=!0)},o(i){m(o,i),l=!1},d(i){i&&v(t),o&&o.d(i)}}}function S(s){let t,l,c;return l=new U({props:{size:s[0]===.5?1:s[0],weight:s[1]==="ghost"?"regular":"medium",$$slots:{default:[tt]},$$scope:{ctx:s}}}),{c(){t=h("div"),W(l.$$.fragment),g(t,"class","c-button--text svelte-1u3rjsd")},m(o,i){p(o,t,i),A(l,t,null),c=!0},p(o,i){const a={};1&i&&(a.size=o[0]===.5?1:o[0]),2&i&&(a.weight=o[1]==="ghost"?"regular":"medium"),1048576&i&&(a.$$scope={dirty:i,ctx:o}),l.$set(a)},i(o){c||(d(l.$$.fragment,o),c=!0)},o(o){m(l.$$.fragment,o),c=!1},d(o){o&&v(t),X(l)}}}function tt(s){let t;const l=s[10].default,c=w(l,s,s[20],null);return{c(){c&&c.c()},m(o,i){c&&c.m(o,i),t=!0},p(o,i){c&&c.p&&(!t||1048576&i)&&C(c,l,o,o[20],t?k(l,o[20],i,null):j(o[20]),null)},i(o){t||(d(c,o),t=!0)},o(o){m(c,o),t=!1},d(o){c&&c.d(o)}}}function V(s){let t,l;const c=s[10].iconRight,o=w(c,s,s[20],J);return{c(){t=h("div"),o&&o.c(),g(t,"class","c-button--icon svelte-1u3rjsd")},m(i,a){p(i,t,a),o&&o.m(t,null),l=!0},p(i,a){o&&o.p&&(!l||1048576&a)&&C(o,c,i,i[20],l?k(c,i[20],a,Z):j(i[20]),J)},i(i){l||(d(o,i),l=!0)},o(i){m(o,i),l=!1},d(i){i&&v(t),o&&o.d(i)}}}function it(s){let t,l,c,o,i,a=s[9].iconLeft&&M(s),r=s[9].default&&S(s),u=s[9].iconRight&&V(s);return{c(){t=h("div"),a&&a.c(),l=F(),r&&r.c(),c=F(),u&&u.c(),g(t,"class",o=G(`c-button--content c-button--size-${s[0]}`)+" svelte-1u3rjsd")},m(n,$){p(n,t,$),a&&a.m(t,null),I(t,l),r&&r.m(t,null),I(t,c),u&&u.m(t,null),i=!0},p(n,$){n[9].iconLeft?a?(a.p(n,$),512&$&&d(a,1)):(a=M(n),a.c(),d(a,1),a.m(t,l)):a&&(b(),m(a,1,1,()=>{a=null}),z()),n[9].default?r?(r.p(n,$),512&$&&d(r,1)):(r=S(n),r.c(),d(r,1),r.m(t,c)):r&&(b(),m(r,1,1,()=>{r=null}),z()),n[9].iconRight?u?(u.p(n,$),512&$&&d(u,1)):(u=V(n),u.c(),d(u,1),u.m(t,null)):u&&(b(),m(u,1,1,()=>{u=null}),z()),(!i||1&$&&o!==(o=G(`c-button--content c-button--size-${n[0]}`)+" svelte-1u3rjsd"))&&g(t,"class",o)},i(n){i||(d(a),d(r),d(u),i=!0)},o(n){m(a),m(r),m(u),i=!1},d(n){n&&v(t),a&&a.d(),r&&r.d(),u&&u.d()}}}function ot(s){let t,l;const c=[{size:s[0]},{variant:s[1]},{color:s[2]},{highContrast:s[3]},{disabled:s[4]},{loading:s[6]},{alignment:s[7]},{radius:s[5]},s[8]];let o={$$slots:{default:[it]},$$scope:{ctx:s}};for(let i=0;i<c.length;i+=1)o=x(o,c[i]);return t=new Y({props:o}),t.$on("click",s[11]),t.$on("keyup",s[12]),t.$on("keydown",s[13]),t.$on("mousedown",s[14]),t.$on("mouseover",s[15]),t.$on("focus",s[16]),t.$on("mouseleave",s[17]),t.$on("blur",s[18]),t.$on("contextmenu",s[19]),{c(){W(t.$$.fragment)},m(i,a){A(t,i,a),l=!0},p(i,[a]){const r=511&a?O(c,[1&a&&{size:i[0]},2&a&&{variant:i[1]},4&a&&{color:i[2]},8&a&&{highContrast:i[3]},16&a&&{disabled:i[4]},64&a&&{loading:i[6]},128&a&&{alignment:i[7]},32&a&&{radius:i[5]},256&a&&P(i[8])]):{};1049091&a&&(r.$$scope={dirty:a,ctx:i}),t.$set(r)},i(i){l||(d(t.$$.fragment,i),l=!0)},o(i){m(t.$$.fragment,i),l=!1},d(i){X(t,i)}}}function st(s,t,l){const c=["size","variant","color","highContrast","disabled","radius","loading","alignment"];let o=E(t,c),{$$slots:i={},$$scope:a}=t;const r=Q(i);let{size:u=2}=t,{variant:n="solid"}=t,{color:$="neutral"}=t,{highContrast:y=!1}=t,{disabled:L=!1}=t,{radius:R="medium"}=t,{loading:B=!1}=t,{alignment:D="center"}=t;return s.$$set=e=>{t=x(x({},t),T(e)),l(8,o=E(t,c)),"size"in e&&l(0,u=e.size),"variant"in e&&l(1,n=e.variant),"color"in e&&l(2,$=e.color),"highContrast"in e&&l(3,y=e.highContrast),"disabled"in e&&l(4,L=e.disabled),"radius"in e&&l(5,R=e.radius),"loading"in e&&l(6,B=e.loading),"alignment"in e&&l(7,D=e.alignment),"$$scope"in e&&l(20,a=e.$$scope)},[u,n,$,y,L,R,B,D,o,r,i,function(e){f.call(this,s,e)},function(e){f.call(this,s,e)},function(e){f.call(this,s,e)},function(e){f.call(this,s,e)},function(e){f.call(this,s,e)},function(e){f.call(this,s,e)},function(e){f.call(this,s,e)},function(e){f.call(this,s,e)},function(e){f.call(this,s,e)},a]}class et extends q{constructor(t){super(),H(this,t,st,ot,N,{size:0,variant:1,color:2,highContrast:3,disabled:4,radius:5,loading:6,alignment:7})}}export{et as B};
