var Yo=Object.defineProperty;var Xo=(r,e,t)=>e in r?Yo(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t;var W=(r,e,t)=>Xo(r,typeof e!="symbol"?e+"":e,t);import{S as ae,i as le,s as oe,b as _e,d as w,f as y,h as _,n as D,k as v,C as ye,an as fn,ao as dt,D as k,V as N,E as S,Y as R,F as x,Q as Ie,ab as _t,t as j,v as V,w as m,u as g,G as C,T as kn,a0 as he,Z as de,X as Q,av as fs,N as ve,a4 as an,J as Oe,a as Ae,$ as gn,K as ze,L as Fe,M as Ze,j as pt,a1 as $n,I as lo,l as We,W as co,at as mt,ak as Pe,a8 as yt,ae as Qo,af as gs,H as Mn,y as An,z as Nn,A as Tn,e as ft,B as En,al as Xn,am as Dt,aj as Ft,a5 as be,a6 as ke,a7 as Me,aA as ei,O as Qn,ac as uo,az as $s,ag as po}from"./SpinnerAugment-uKUHz-bK.js";import{G as mo,D as Zn,s as Un,S as ti,a as ct,P as ln,C as ni,B as es,T as ts,b as fo,c as si}from"./github-DC7nlEoK.js";import"./design-system-init-DDX-Gvwz.js";import{W as ue,a as qe,e as ce,u as Ke,o as Ye,I as Zt,h as Ne,i as ri,b as oi,H as hs}from"./IconButtonAugment-CQzh_Hae.js";import{M as go,R as ii}from"./message-broker-DdVtH9Vr.js";import{P as vs,G as $o,S as ho,a as vo,N as yo,L as wo,b as $e,M as Ve,D as So,F as xo,R as ai,f as Co,c as li,T as _o,d as ci,C as di,e as ui,g as pi,h as mi,A as fi,i as gi,j as $i,U as hi,k as vi}from"./partner-mcp-utils-Di7HqDS-.js";import{H as Rt,N as Te,L as X,r as st,I as Hn,T as bt,D as xe,O as yi,d as Dn,P as pn,A as ys,b as wi,c as Si}from"./index-DP6mqmYw.js";import{V as bo}from"./VSCodeCodicon-n5HCoiGq.js";import{o as xi}from"./keypress-DD1aQVr0.js";import{A as Ci}from"./async-messaging-D4p6YcQf.js";import{D as _i}from"./Drawer-DUR257aQ.js";import{B as we}from"./ButtonAugment-D5QDitBR.js";import{T as rt,a as ns}from"./CardAugment-BqjOeIg4.js";import{C as In}from"./CalloutAugment-CznTrv4g.js";import{E as bi}from"./ellipsis-Btdwvghx.js";import{P as ki}from"./pen-to-square-BWYRDHTI.js";import{T as ko}from"./TextAreaAugment-CoQvc_01.js";import{C as Mi,S as Ai}from"./copy-CfR4-ke6.js";import{C as Mo,a as Ni,T as ss}from"./CollapseButtonAugment-ffrJmKr6.js";import{C as rs,E as Ti}from"./chevron-down-BMBumfK8.js";import{M as Ei}from"./index-D0JCd9Au.js";import{M as Ii,R as Ri}from"./rules-model-BZSbJKoe.js";import{C as Pi,E as Li}from"./chat-flags-model-BrC28MCB.js";import{R as Oi}from"./RulesModeSelector-D49_1uJD.js";import{M as Ao}from"./ModalAugment-CtjgW1F_.js";import"./BaseTextInput-BTYl5feP.js";import"./index-GYuo8qik.js";import"./types-CGlLNakm.js";import"./file-paths-CAgP5Fvb.js";import"./index-Bcx5x-t6.js";import"./lodash-rqaON3VT.js";const jt={maxMS:9e5,initialMS:6e4,mult:2,maxSteps:4};let zi=class{constructor(r,e=jt){W(this,"timerId",null);W(this,"currentMS");W(this,"step",0);W(this,"params");this.callback=r;const t={...e};t.maxMS<0&&(console.warn("PollingManager: Negative maxMS detected, using default value of 15 minutes"),t.maxMS=jt.maxMS),t.initialMS<=0&&(console.warn("PollingManager: Negative or zero initialMS detected, using default value of 1 minute"),t.initialMS=jt.initialMS),t.mult<=0&&(console.warn("PollingManager: Negative or zero multiplier detected, using default value of 2"),t.mult=jt.mult),t.maxSteps!==void 0&&t.maxSteps<0&&(console.warn("PollingManager: Negative maxSteps detected, using default value of 4"),t.maxSteps=jt.maxSteps),this.params=t,this.currentMS=this.params.maxMS}startPolling(){this.stopPolling(),this.currentMS=this.params.initialMS,this.step=0,this.safeExecute(),this.scheduleNext()}stopPolling(){this.timerId!==null&&(window.clearTimeout(this.timerId),this.timerId=null)}dispose(){this.stopPolling()}scheduleNext(){this.timerId=window.setTimeout(()=>{if(this.safeExecute(),this.params.maxMS===0){if(this.step++,this.params.maxSteps!==void 0&&this.step>=this.params.maxSteps)return void this.stopPolling()}else this.currentMS<this.params.maxMS&&(this.step++,this.params.maxSteps!==void 0&&this.step>=this.params.maxSteps?(this.currentMS=this.params.maxMS,this.step=0):this.currentMS=Math.min(this.currentMS*this.params.mult,this.params.maxMS));this.scheduleNext()},this.currentMS)}safeExecute(){try{const r=this.callback();r instanceof Promise&&r.catch(e=>console.error("Error in polling callback:",e))}catch(r){console.error("Error in polling callback:",r)}}};function Fi(r){let e,t,n;return{c(){e=_e("svg"),t=_e("path"),n=_e("path"),w(t,"d","M2.43703 10.7785C2.30998 10.978 2.16478 11.2137 2.05588 11.3951C1.94698 11.5764 2.00143 11.8121 2.18293 11.921L4.66948 13.4442C4.85098 13.553 5.08695 13.4986 5.19585 13.3173C5.2866 13.1541 5.41365 12.9365 5.55885 12.7007C6.53895 11.0868 7.5372 11.2681 9.3159 12.1204L11.7843 13.281C11.9839 13.3717 12.2017 13.281 12.2925 13.0997L13.4722 10.4339C13.563 10.2526 13.4722 10.0169 13.2907 9.92619C12.7644 9.69044 11.7298 9.20084 10.8223 8.74749C7.44645 7.13354 4.59689 7.24234 2.43703 10.7785Z"),w(t,"fill","currentColor"),w(n,"d","M13.563 4.72157C13.69 4.52209 13.8352 4.28635 13.9441 4.105C14.053 3.92366 13.9985 3.68791 13.817 3.57911L11.3305 2.05583C11.149 1.94702 10.913 2.00143 10.8041 2.18277C10.7134 2.34598 10.5863 2.56359 10.4411 2.79934C9.461 4.41329 8.46275 4.23194 6.68405 3.37963L4.21563 2.21904C4.01598 2.12837 3.79818 2.21904 3.70743 2.40038L2.52767 5.0661C2.43692 5.24745 2.52767 5.4832 2.70917 5.5739C3.23552 5.80965 4.27007 6.29925 5.1776 6.7526C8.53535 8.34845 11.3849 8.25775 13.563 4.72157Z"),w(n,"fill","currentColor"),w(e,"width","15"),w(e,"height","15"),w(e,"viewBox","0 0 15 15"),w(e,"fill","none"),w(e,"xmlns","http://www.w3.org/2000/svg")},m(s,o){y(s,e,o),_(e,t),_(e,n)},p:D,i:D,o:D,d(s){s&&v(e)}}}class No extends ae{constructor(e){super(),le(this,e,null,Fi,oe,{})}}function Zi(r){let e,t;return{c(){e=_e("svg"),t=_e("path"),w(t,"fill-rule","evenodd"),w(t,"clip-rule","evenodd"),w(t,"d","M13.5028 2H7.7257C7.7257 3.44 8.8914 4.60571 10.3314 4.60571H11.3942V5.6343C11.3942 7.0743 12.5599 8.24 14 8.24V2.49714C14 2.22285 13.7771 2 13.5028 2ZM10.6399 4.88H4.86279C4.86279 6.32 6.0285 7.4857 7.4685 7.4857H8.53135V8.5143C8.53135 9.9543 9.69705 11.12 11.137 11.12V5.37715C11.137 5.10285 10.9142 4.88 10.6399 4.88ZM2 7.75995H7.7771C8.0514 7.75995 8.27425 7.9828 8.27425 8.2571V13.9999C6.83425 13.9999 5.66855 12.8342 5.66855 11.3942V10.3656H4.6057C3.16571 10.3656 2 9.19995 2 7.75995Z"),w(t,"fill","currentColor"),w(e,"width","15"),w(e,"height","15"),w(e,"viewBox","0 0 15 15"),w(e,"fill","none"),w(e,"xmlns","http://www.w3.org/2000/svg")},m(n,s){y(n,e,s),_(e,t)},p:D,i:D,o:D,d(n){n&&v(e)}}}class To extends ae{constructor(e){super(),le(this,e,null,Zi,oe,{})}}function Ui(r){let e,t;return{c(){e=_e("svg"),t=_e("path"),w(t,"d","M3 2.5C3 2.22386 3.22386 2 3.5 2H9.08579C9.21839 2 9.34557 2.05268 9.43934 2.14645L11.8536 4.56066C11.9473 4.65443 12 4.78161 12 4.91421V12.5C12 12.7761 11.7761 13 11.5 13H3.5C3.22386 13 3 12.7761 3 12.5V2.5ZM3.5 1C2.67157 1 2 1.67157 2 2.5V12.5C2 13.3284 2.67157 14 3.5 14H11.5C12.3284 14 13 13.3284 13 12.5V4.91421C13 4.51639 12.842 4.13486 12.5607 3.85355L10.1464 1.43934C9.86514 1.15804 9.48361 1 9.08579 1H3.5ZM4.5 4C4.22386 4 4 4.22386 4 4.5C4 4.77614 4.22386 5 4.5 5H7.5C7.77614 5 8 4.77614 8 4.5C8 4.22386 7.77614 4 7.5 4H4.5ZM4.5 7C4.22386 7 4 7.22386 4 7.5C4 7.77614 4.22386 8 4.5 8H10.5C10.7761 8 11 7.77614 11 7.5C11 7.22386 10.7761 7 10.5 7H4.5ZM4.5 10C4.22386 10 4 10.2239 4 10.5C4 10.7761 4.22386 11 4.5 11H10.5C10.7761 11 11 10.7761 11 10.5C11 10.2239 10.7761 10 10.5 10H4.5Z"),w(t,"fill","currentColor"),w(t,"fill-rule","evenodd"),w(t,"clip-rule","evenodd"),w(e,"width","15"),w(e,"height","15"),w(e,"viewBox","0 0 15 15"),w(e,"fill","none"),w(e,"xmlns","http://www.w3.org/2000/svg")},m(n,s){y(n,e,s),_(e,t)},p:D,i:D,o:D,d(n){n&&v(e)}}}class Ee extends ae{constructor(e){super(),le(this,e,null,Ui,oe,{})}}function Di(r){let e,t;return{c(){e=_e("svg"),t=_e("path"),w(t,"fill-rule","evenodd"),w(t,"clip-rule","evenodd"),w(t,"d","M3.5 2C3.22386 2 3 2.22386 3 2.5V12.5C3 12.7761 3.22386 13 3.5 13H11.5C11.7761 13 12 12.7761 12 12.5V4.70711L9.29289 2H3.5ZM2 2.5C2 1.67157 2.67157 1 3.5 1H9.5C9.63261 1 9.75979 1.05268 9.85355 1.14645L12.7803 4.07322C12.921 4.21388 13 4.40464 13 4.60355V12.5C13 13.3284 12.3284 14 11.5 14H3.5C2.67157 14 2 13.3284 2 12.5V2.5ZM4.75 7.5C4.75 7.22386 4.97386 7 5.25 7H9.75C10.0261 7 10.25 7.22386 10.25 7.5C10.25 7.77614 10.0261 8 9.75 8H5.25C4.97386 8 4.75 7.77614 4.75 7.5Z"),w(t,"fill","currentColor"),w(e,"width","15"),w(e,"height","15"),w(e,"viewBox","0 0 15 15"),w(e,"fill","none"),w(e,"xmlns","http://www.w3.org/2000/svg")},m(n,s){y(n,e,s),_(e,t)},p:D,i:D,o:D,d(n){n&&v(e)}}}class Eo extends ae{constructor(e){super(),le(this,e,null,Di,oe,{})}}var qn;let ws=(qn=class{constructor(r){W(this,"configs",ye([]));W(this,"pollingManager");W(this,"_enableDebugFeatures",ye(!1));W(this,"_settingsComponentSupported",ye({workspaceContext:!1,mcpServerList:!1,mcpServerImport:!1,orientation:!1,remoteTools:!1,userGuidelines:!1,terminal:!1,rules:!1}));W(this,"_enableAgentMode",ye(!1));W(this,"_enableAgentSwarmMode",ye(!1));W(this,"_enableNativeRemoteMcp",ye(!0));W(this,"_hasEverUsedRemoteAgent",ye(!1));W(this,"_enableInitialOrientation",ye(!1));W(this,"_userTier",ye("unknown"));W(this,"_guidelines",ye({}));this._host=r,this.pollingManager=new zi(()=>this.requestToolStatus(!1),{maxMS:0,initialMS:2e3,mult:1,maxSteps:150}),this.requestToolStatus(!1)}transformToolDisplay(r){const e=!r.isConfigured,t=r.oauthUrl;if(r.identifier.hostName===Rt.remoteToolHost){let n=r.identifier.toolId;switch(typeof n=="string"&&/^\d+$/.test(n)&&(n=Number(n)),n){case Te.GitHubApi:return{displayName:"GitHub",description:"Configure GitHub API access for repository operations",icon:mo,requiresAuthentication:e,authUrl:t};case Te.Linear:return{displayName:"Linear",description:"Configure Linear API access for issue tracking",icon:wo,requiresAuthentication:e,authUrl:t};case Te.Jira:return{displayName:"Jira",description:"Configure Jira API access for issue tracking",icon:To,requiresAuthentication:e,authUrl:t};case Te.Notion:return{displayName:"Notion",description:"Configure Notion API access",icon:yo,requiresAuthentication:e,authUrl:t};case Te.Confluence:return{displayName:"Confluence",description:"Configure Confluence API access",icon:No,requiresAuthentication:e,authUrl:t};case Te.WebSearch:return{displayName:"Web Search",description:"Configure web search capabilities",icon:vo,requiresAuthentication:e,authUrl:t};case Te.Supabase:return{displayName:"Supabase",description:"Configure Supabase API access",icon:ho,requiresAuthentication:e,authUrl:t};case Te.Glean:return{displayName:"Glean",description:"Configure Glean API access",icon:$o,requiresAuthentication:e,authUrl:t};case Te.Unknown:return{displayName:"Unknown",description:"Unknown tool",requiresAuthentication:e,authUrl:t};default:throw new Error(`Unhandled RemoteToolId: ${n}`)}}else if(r.identifier.hostName===Rt.localToolHost){const n=r.identifier.toolId;switch(n){case X.readFile:case X.editFile:case X.saveFile:case X.launchProcess:case X.killProcess:case X.readProcess:case X.writeProcess:case X.listProcesses:case X.waitProcess:case X.openBrowser:case X.clarify:case X.onboardingSubAgent:case X.strReplaceEditor:case X.remember:case X.diagnostics:case X.setupScript:case X.readTerminal:case X.gitCommitRetrieval:case X.memoryRetrieval:case X.startWorkerAgent:case X.readWorkerState:case X.waitForWorkerAgent:case X.sendInstructionToWorkerAgent:case X.stopWorkerAgent:case X.deleteWorkerAgent:case X.readWorkerAgentEdits:case X.LocalSubAgent:return{displayName:r.definition.name.toString(),description:"Local tool",icon:Ee,requiresAuthentication:e,authUrl:t};default:throw new Error(`Unhandled LocalToolType: ${n}`)}}else if(r.identifier.hostName===Rt.sidecarToolHost){const n=r.identifier.toolId;switch(n){case $e.codebaseRetrieval:return{displayName:"Code Search",description:"Configure codebase search capabilities",icon:Ve,requiresAuthentication:e,authUrl:t};case $e.shell:return{displayName:"Shell",description:"Shell",icon:Ve,requiresAuthentication:e,authUrl:t};case $e.strReplaceEditor:return{displayName:"File Edit",description:"File Editor",icon:Ve,requiresAuthentication:e,authUrl:t};case $e.view:return{displayName:"File View",description:"File Viewer",icon:Ve,requiresAuthentication:e,authUrl:t};case $e.webFetch:return{displayName:"Web Fetch",description:"Retrieve information from the web",icon:Ve,requiresAuthentication:e,authUrl:t};case $e.removeFiles:return{displayName:"Remove Files",description:"Remove files from the codebase",icon:Eo,requiresAuthentication:e,authUrl:t};case $e.remember:return{displayName:r.definition.name.toString(),description:"Remember",icon:Ee,requiresAuthentication:e,authUrl:t};case $e.saveFile:return{displayName:"Save File",description:"Save a new file",icon:xo,requiresAuthentication:e,authUrl:t};case $e.viewTaskList:return{displayName:"View Task List",description:"View the current task list",icon:Ee,requiresAuthentication:e,authUrl:t};case $e.reorganizeTaskList:return{displayName:"Reorganize Task List",description:"Reorganize the task list structure for major restructuring",icon:Ee,requiresAuthentication:e,authUrl:t};case $e.viewRangeUntruncated:return{displayName:r.definition.name.toString(),description:"View Range",icon:Ee,requiresAuthentication:e,authUrl:t};case $e.updateTasks:return{displayName:"Update Tasks",description:"Update one or more tasks in the task list",icon:Ee,requiresAuthentication:e,authUrl:t};case $e.addTasks:return{displayName:"Add Tasks",description:"Add one or more new tasks to the task list",icon:Ee,requiresAuthentication:e,authUrl:t};case $e.searchUntruncated:return{displayName:r.definition.name.toString(),description:"Search Untruncated",icon:Ee,requiresAuthentication:e,authUrl:t};case $e.renderMermaid:return{displayName:"View Mermaid Diagram",description:"View a mermaid diagram",icon:So,requiresAuthentication:e,authUrl:t};case $e.grepSearch:return{displayName:"Grep search",description:"Run grep search",icon:Ve,requiresAuthentication:e,authUrl:t};default:throw new Error(`Unhandled SidecarToolType: ${n}`)}}return{displayName:r.definition.name.toString(),description:r.definition.description||"",requiresAuthentication:e,authUrl:t}}handleMessageFromExtension(r){const e=r.data;switch(e.type){case ue.toolConfigInitialize:return this.createConfigsFromHostTools(e.data.hostTools,e.data.toolConfigs),e.data&&e.data.enableDebugFeatures!==void 0&&this._enableDebugFeatures.set(e.data.enableDebugFeatures),e.data&&e.data.settingsComponentSupported!==void 0&&this._settingsComponentSupported.set(e.data.settingsComponentSupported),e.data.enableAgentMode!==void 0&&this._enableAgentMode.set(e.data.enableAgentMode),e.data.enableAgentSwarmMode!==void 0&&this._enableAgentSwarmMode.set(e.data.enableAgentSwarmMode),e.data.hasEverUsedRemoteAgent!==void 0&&this._hasEverUsedRemoteAgent.set(e.data.hasEverUsedRemoteAgent),e.data.enableInitialOrientation!==void 0&&this._enableInitialOrientation.set(e.data.enableInitialOrientation),e.data.userTier!==void 0&&this._userTier.set(e.data.userTier),e.data.guidelines!==void 0&&this._guidelines.set(e.data.guidelines),e.data.enableNativeRemoteMcp!==void 0&&this._enableNativeRemoteMcp.set(e.data.enableNativeRemoteMcp),!0;case ue.toolConfigDefinitionsResponse:return this.configs.update(t=>this.createConfigsFromHostTools(e.data.hostTools,[]).map(n=>{const s=t.find(o=>o.name===n.name);return s?{...s,displayName:n.displayName,description:n.description,icon:n.icon,requiresAuthentication:n.requiresAuthentication,authUrl:n.authUrl,isConfigured:n.isConfigured,toolApprovalConfig:n.toolApprovalConfig}:n})),!0}return!1}createConfigsFromHostTools(r,e){return r.map(t=>{const n=this.transformToolDisplay(t),s=e.find(i=>i.name===t.definition.name),o=(s==null?void 0:s.isConfigured)??!n.requiresAuthentication;return{config:(s==null?void 0:s.config)??{},configString:JSON.stringify((s==null?void 0:s.config)??{},null,2),isConfigured:o,name:t.definition.name.toString(),displayName:n.displayName,description:n.description,identifier:t.identifier,icon:n.icon,requiresAuthentication:n.requiresAuthentication,authUrl:n.authUrl,showStatus:!1,statusMessage:"",statusType:"info",toolApprovalConfig:t.toolApprovalConfig}})}getConfigs(){return this.configs}isDisplayableTool(r){return["github","linear","notion","jira","confluence","supabase"].includes(r.displayName.toLowerCase())}getDisplayableTools(){return fn(this.configs,r=>{const e=r.filter(n=>this.isDisplayableTool(n)),t=new Map;for(const n of e)t.set(n.displayName,n);return Array.from(t.values()).sort((n,s)=>{const o={GitHub:1,Linear:2,Notion:3},i=Number.MAX_SAFE_INTEGER,l=o[n.displayName]||i,a=o[s.displayName]||i;return l<i&&a<i||l===i&&a===i?l!==a?l-a:n.displayName.localeCompare(s.displayName):l-a})})}getPretendNativeToolDefs(){return fn(this.configs,r=>this.getEnableNativeRemoteMcp()?r.filter(e=>e.name===vs.STRIPE||e.name===vs.SENTRY):[])}saveConfig(r){this.startPolling()}notifyLoaded(){this._host.postMessage({type:ue.toolConfigLoaded})}startPolling(){this.pollingManager.startPolling()}requestToolStatus(r=!0){this._host.postMessage({type:ue.toolConfigGetDefinitions,data:{useCache:r}})}dispose(){this.pollingManager.dispose()}getEnableDebugFeatures(){return this._enableDebugFeatures}getEnableAgentMode(){return this._enableAgentMode}getEnableAgentSwarmMode(){return this._enableAgentSwarmMode}getEnableNativeRemoteMcp(){return this._host.clientType==="vscode"&&this._enableNativeRemoteMcp}getHasEverUsedRemoteAgent(){return this._hasEverUsedRemoteAgent}getEnableInitialOrientation(){return this._enableInitialOrientation}getUserTier(){return this._userTier}getGuidelines(){return this._guidelines}updateLocalUserGuidelines(r){this._guidelines.update(e=>e.userGuidelines?{...e,userGuidelines:{...e.userGuidelines,contents:r,enabled:r.length>0}}:e)}updateToolApprovalConfig(r,e){this.configs.update(t=>t.map(n=>n.identifier.toolId===r.toolId&&n.identifier.hostName===r.hostName?{...n,toolApprovalConfig:e}:n))}getSettingsComponentSupported(){return this._settingsComponentSupported}},W(qn,"key","toolConfigModel"),qn);var me,Bn;(function(r){r.assertEqual=e=>e,r.assertIs=function(e){},r.assertNever=function(e){throw new Error},r.arrayToEnum=e=>{const t={};for(const n of e)t[n]=n;return t},r.getValidEnumValues=e=>{const t=r.objectKeys(e).filter(s=>typeof e[e[s]]!="number"),n={};for(const s of t)n[s]=e[s];return r.objectValues(n)},r.objectValues=e=>r.objectKeys(e).map(function(t){return e[t]}),r.objectKeys=typeof Object.keys=="function"?e=>Object.keys(e):e=>{const t=[];for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.push(n);return t},r.find=(e,t)=>{for(const n of e)if(t(n))return n},r.isInteger=typeof Number.isInteger=="function"?e=>Number.isInteger(e):e=>typeof e=="number"&&isFinite(e)&&Math.floor(e)===e,r.joinValues=function(e,t=" | "){return e.map(n=>typeof n=="string"?`'${n}'`:n).join(t)},r.jsonStringifyReplacer=(e,t)=>typeof t=="bigint"?t.toString():t})(me||(me={})),function(r){r.mergeShapes=(e,t)=>({...e,...t})}(Bn||(Bn={}));const B=me.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),et=r=>{switch(typeof r){case"undefined":return B.undefined;case"string":return B.string;case"number":return isNaN(r)?B.nan:B.number;case"boolean":return B.boolean;case"function":return B.function;case"bigint":return B.bigint;case"symbol":return B.symbol;case"object":return Array.isArray(r)?B.array:r===null?B.null:r.then&&typeof r.then=="function"&&r.catch&&typeof r.catch=="function"?B.promise:typeof Map<"u"&&r instanceof Map?B.map:typeof Set<"u"&&r instanceof Set?B.set:typeof Date<"u"&&r instanceof Date?B.date:B.object;default:return B.unknown}},L=me.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class Ue extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=n=>{this.issues=[...this.issues,n]},this.addIssues=(n=[])=>{this.issues=[...this.issues,...n]};const t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){const t=e||function(o){return o.message},n={_errors:[]},s=o=>{for(const i of o.issues)if(i.code==="invalid_union")i.unionErrors.map(s);else if(i.code==="invalid_return_type")s(i.returnTypeError);else if(i.code==="invalid_arguments")s(i.argumentsError);else if(i.path.length===0)n._errors.push(t(i));else{let l=n,a=0;for(;a<i.path.length;){const c=i.path[a];a===i.path.length-1?(l[c]=l[c]||{_errors:[]},l[c]._errors.push(t(i))):l[c]=l[c]||{_errors:[]},l=l[c],a++}}};return s(this),n}static assert(e){if(!(e instanceof Ue))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,me.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(e=t=>t.message){const t={},n=[];for(const s of this.issues)s.path.length>0?(t[s.path[0]]=t[s.path[0]]||[],t[s.path[0]].push(e(s))):n.push(e(s));return{formErrors:n,fieldErrors:t}}get formErrors(){return this.flatten()}}Ue.create=r=>new Ue(r);const Lt=(r,e)=>{let t;switch(r.code){case L.invalid_type:t=r.received===B.undefined?"Required":`Expected ${r.expected}, received ${r.received}`;break;case L.invalid_literal:t=`Invalid literal value, expected ${JSON.stringify(r.expected,me.jsonStringifyReplacer)}`;break;case L.unrecognized_keys:t=`Unrecognized key(s) in object: ${me.joinValues(r.keys,", ")}`;break;case L.invalid_union:t="Invalid input";break;case L.invalid_union_discriminator:t=`Invalid discriminator value. Expected ${me.joinValues(r.options)}`;break;case L.invalid_enum_value:t=`Invalid enum value. Expected ${me.joinValues(r.options)}, received '${r.received}'`;break;case L.invalid_arguments:t="Invalid function arguments";break;case L.invalid_return_type:t="Invalid function return type";break;case L.invalid_date:t="Invalid date";break;case L.invalid_string:typeof r.validation=="object"?"includes"in r.validation?(t=`Invalid input: must include "${r.validation.includes}"`,typeof r.validation.position=="number"&&(t=`${t} at one or more positions greater than or equal to ${r.validation.position}`)):"startsWith"in r.validation?t=`Invalid input: must start with "${r.validation.startsWith}"`:"endsWith"in r.validation?t=`Invalid input: must end with "${r.validation.endsWith}"`:me.assertNever(r.validation):t=r.validation!=="regex"?`Invalid ${r.validation}`:"Invalid";break;case L.too_small:t=r.type==="array"?`Array must contain ${r.exact?"exactly":r.inclusive?"at least":"more than"} ${r.minimum} element(s)`:r.type==="string"?`String must contain ${r.exact?"exactly":r.inclusive?"at least":"over"} ${r.minimum} character(s)`:r.type==="number"?`Number must be ${r.exact?"exactly equal to ":r.inclusive?"greater than or equal to ":"greater than "}${r.minimum}`:r.type==="date"?`Date must be ${r.exact?"exactly equal to ":r.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(r.minimum))}`:"Invalid input";break;case L.too_big:t=r.type==="array"?`Array must contain ${r.exact?"exactly":r.inclusive?"at most":"less than"} ${r.maximum} element(s)`:r.type==="string"?`String must contain ${r.exact?"exactly":r.inclusive?"at most":"under"} ${r.maximum} character(s)`:r.type==="number"?`Number must be ${r.exact?"exactly":r.inclusive?"less than or equal to":"less than"} ${r.maximum}`:r.type==="bigint"?`BigInt must be ${r.exact?"exactly":r.inclusive?"less than or equal to":"less than"} ${r.maximum}`:r.type==="date"?`Date must be ${r.exact?"exactly":r.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(r.maximum))}`:"Invalid input";break;case L.custom:t="Invalid input";break;case L.invalid_intersection_types:t="Intersection results could not be merged";break;case L.not_multiple_of:t=`Number must be a multiple of ${r.multipleOf}`;break;case L.not_finite:t="Number must be finite";break;default:t=e.defaultError,me.assertNever(r)}return{message:t}};let Io=Lt;function hn(){return Io}const vn=r=>{const{data:e,path:t,errorMaps:n,issueData:s}=r,o=[...t,...s.path||[]],i={...s,path:o};if(s.message!==void 0)return{...s,path:o,message:s.message};let l="";const a=n.filter(c=>!!c).slice().reverse();for(const c of a)l=c(i,{data:e,defaultError:l}).message;return{...s,path:o,message:l}};function q(r,e){const t=hn(),n=vn({issueData:e,data:r.data,path:r.path,errorMaps:[r.common.contextualErrorMap,r.schemaErrorMap,t,t===Lt?void 0:Lt].filter(s=>!!s)});r.common.issues.push(n)}class Re{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(e,t){const n=[];for(const s of t){if(s.status==="aborted")return te;s.status==="dirty"&&e.dirty(),n.push(s.value)}return{status:e.value,value:n}}static async mergeObjectAsync(e,t){const n=[];for(const s of t){const o=await s.key,i=await s.value;n.push({key:o,value:i})}return Re.mergeObjectSync(e,n)}static mergeObjectSync(e,t){const n={};for(const s of t){const{key:o,value:i}=s;if(o.status==="aborted"||i.status==="aborted")return te;o.status==="dirty"&&e.dirty(),i.status==="dirty"&&e.dirty(),o.value==="__proto__"||i.value===void 0&&!s.alwaysSet||(n[o.value]=i.value)}return{status:e.value,value:n}}}const te=Object.freeze({status:"aborted"}),yn=r=>({status:"dirty",value:r}),Le=r=>({status:"valid",value:r}),Gn=r=>r.status==="aborted",Jn=r=>r.status==="dirty",kt=r=>r.status==="valid",Gt=r=>typeof Promise<"u"&&r instanceof Promise;function wn(r,e,t,n){if(typeof e=="function"?r!==e||!n:!e.has(r))throw new TypeError("Cannot read private member from an object whose class did not declare it");return e.get(r)}function Ro(r,e,t,n,s){if(typeof e=="function"?r!==e||!s:!e.has(r))throw new TypeError("Cannot write private member to an object whose class did not declare it");return e.set(r,t),t}var K,qt,Ht;typeof SuppressedError=="function"&&SuppressedError,function(r){r.errToObj=e=>typeof e=="string"?{message:e}:e||{},r.toString=e=>typeof e=="string"?e:e==null?void 0:e.message}(K||(K={}));class Xe{constructor(e,t,n,s){this._cachedPath=[],this.parent=e,this.data=t,this._path=n,this._key=s}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const Ss=(r,e)=>{if(kt(e))return{success:!0,data:e.value};if(!r.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const t=new Ue(r.common.issues);return this._error=t,this._error}}};function se(r){if(!r)return{};const{errorMap:e,invalid_type_error:t,required_error:n,description:s}=r;if(e&&(t||n))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return e?{errorMap:e,description:s}:{errorMap:(o,i)=>{var l,a;const{message:c}=r;return o.code==="invalid_enum_value"?{message:c??i.defaultError}:i.data===void 0?{message:(l=c??n)!==null&&l!==void 0?l:i.defaultError}:o.code!=="invalid_type"?{message:i.defaultError}:{message:(a=c??t)!==null&&a!==void 0?a:i.defaultError}},description:s}}class re{get description(){return this._def.description}_getType(e){return et(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:et(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new Re,ctx:{common:e.parent.common,data:e.data,parsedType:et(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){const t=this._parse(e);if(Gt(t))throw new Error("Synchronous parse encountered promise.");return t}_parseAsync(e){const t=this._parse(e);return Promise.resolve(t)}parse(e,t){const n=this.safeParse(e,t);if(n.success)return n.data;throw n.error}safeParse(e,t){var n;const s={common:{issues:[],async:(n=t==null?void 0:t.async)!==null&&n!==void 0&&n,contextualErrorMap:t==null?void 0:t.errorMap},path:(t==null?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:et(e)},o=this._parseSync({data:e,path:s.path,parent:s});return Ss(s,o)}"~validate"(e){var t,n;const s={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:et(e)};if(!this["~standard"].async)try{const o=this._parseSync({data:e,path:[],parent:s});return kt(o)?{value:o.value}:{issues:s.common.issues}}catch(o){!((n=(t=o==null?void 0:o.message)===null||t===void 0?void 0:t.toLowerCase())===null||n===void 0)&&n.includes("encountered")&&(this["~standard"].async=!0),s.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:s}).then(o=>kt(o)?{value:o.value}:{issues:s.common.issues})}async parseAsync(e,t){const n=await this.safeParseAsync(e,t);if(n.success)return n.data;throw n.error}async safeParseAsync(e,t){const n={common:{issues:[],contextualErrorMap:t==null?void 0:t.errorMap,async:!0},path:(t==null?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:et(e)},s=this._parse({data:e,path:n.path,parent:n}),o=await(Gt(s)?s:Promise.resolve(s));return Ss(n,o)}refine(e,t){const n=s=>typeof t=="string"||t===void 0?{message:t}:typeof t=="function"?t(s):t;return this._refinement((s,o)=>{const i=e(s),l=()=>o.addIssue({code:L.custom,...n(s)});return typeof Promise<"u"&&i instanceof Promise?i.then(a=>!!a||(l(),!1)):!!i||(l(),!1)})}refinement(e,t){return this._refinement((n,s)=>!!e(n)||(s.addIssue(typeof t=="function"?t(n,s):t),!1))}_refinement(e){return new Be({schema:this,typeName:ee.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:t=>this["~validate"](t)}}optional(){return Je.create(this,this._def)}nullable(){return vt.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return Ge.create(this)}promise(){return zt.create(this,this._def)}or(e){return Yt.create([this,e],this._def)}and(e){return Xt.create(this,e,this._def)}transform(e){return new Be({...se(this._def),schema:this,typeName:ee.ZodEffects,effect:{type:"transform",transform:e}})}default(e){const t=typeof e=="function"?e:()=>e;return new nn({...se(this._def),innerType:this,defaultValue:t,typeName:ee.ZodDefault})}brand(){return new os({typeName:ee.ZodBranded,type:this,...se(this._def)})}catch(e){const t=typeof e=="function"?e:()=>e;return new sn({...se(this._def),innerType:this,catchValue:t,typeName:ee.ZodCatch})}describe(e){return new this.constructor({...this._def,description:e})}pipe(e){return cn.create(this,e)}readonly(){return rn.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const ji=/^c[^\s-]{8,}$/i,Vi=/^[0-9a-z]+$/,qi=/^[0-9A-HJKMNP-TV-Z]{26}$/i,Hi=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,Bi=/^[a-z0-9_-]{21}$/i,Gi=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,Ji=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,Wi=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i;let jn;const Ki=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,Yi=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,Xi=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,Qi=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,ea=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,ta=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,Po="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",na=new RegExp(`^${Po}$`);function Lo(r){let e="([01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d";return r.precision?e=`${e}\\.\\d{${r.precision}}`:r.precision==null&&(e=`${e}(\\.\\d+)?`),e}function Oo(r){let e=`${Po}T${Lo(r)}`;const t=[];return t.push(r.local?"Z?":"Z"),r.offset&&t.push("([+-]\\d{2}:?\\d{2})"),e=`${e}(${t.join("|")})`,new RegExp(`^${e}$`)}function sa(r,e){if(!Gi.test(r))return!1;try{const[t]=r.split("."),n=t.replace(/-/g,"+").replace(/_/g,"/").padEnd(t.length+(4-t.length%4)%4,"="),s=JSON.parse(atob(n));return typeof s=="object"&&s!==null&&!(!s.typ||!s.alg)&&(!e||s.alg===e)}catch{return!1}}function ra(r,e){return!(e!=="v4"&&e||!Yi.test(r))||!(e!=="v6"&&e||!Qi.test(r))}class He extends re{_parse(e){if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==B.string){const i=this._getOrReturnCtx(e);return q(i,{code:L.invalid_type,expected:B.string,received:i.parsedType}),te}const t=new Re;let n;for(const i of this._def.checks)if(i.kind==="min")e.data.length<i.value&&(n=this._getOrReturnCtx(e,n),q(n,{code:L.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),t.dirty());else if(i.kind==="max")e.data.length>i.value&&(n=this._getOrReturnCtx(e,n),q(n,{code:L.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),t.dirty());else if(i.kind==="length"){const l=e.data.length>i.value,a=e.data.length<i.value;(l||a)&&(n=this._getOrReturnCtx(e,n),l?q(n,{code:L.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}):a&&q(n,{code:L.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}),t.dirty())}else if(i.kind==="email")Wi.test(e.data)||(n=this._getOrReturnCtx(e,n),q(n,{validation:"email",code:L.invalid_string,message:i.message}),t.dirty());else if(i.kind==="emoji")jn||(jn=new RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),jn.test(e.data)||(n=this._getOrReturnCtx(e,n),q(n,{validation:"emoji",code:L.invalid_string,message:i.message}),t.dirty());else if(i.kind==="uuid")Hi.test(e.data)||(n=this._getOrReturnCtx(e,n),q(n,{validation:"uuid",code:L.invalid_string,message:i.message}),t.dirty());else if(i.kind==="nanoid")Bi.test(e.data)||(n=this._getOrReturnCtx(e,n),q(n,{validation:"nanoid",code:L.invalid_string,message:i.message}),t.dirty());else if(i.kind==="cuid")ji.test(e.data)||(n=this._getOrReturnCtx(e,n),q(n,{validation:"cuid",code:L.invalid_string,message:i.message}),t.dirty());else if(i.kind==="cuid2")Vi.test(e.data)||(n=this._getOrReturnCtx(e,n),q(n,{validation:"cuid2",code:L.invalid_string,message:i.message}),t.dirty());else if(i.kind==="ulid")qi.test(e.data)||(n=this._getOrReturnCtx(e,n),q(n,{validation:"ulid",code:L.invalid_string,message:i.message}),t.dirty());else if(i.kind==="url")try{new URL(e.data)}catch{n=this._getOrReturnCtx(e,n),q(n,{validation:"url",code:L.invalid_string,message:i.message}),t.dirty()}else i.kind==="regex"?(i.regex.lastIndex=0,i.regex.test(e.data)||(n=this._getOrReturnCtx(e,n),q(n,{validation:"regex",code:L.invalid_string,message:i.message}),t.dirty())):i.kind==="trim"?e.data=e.data.trim():i.kind==="includes"?e.data.includes(i.value,i.position)||(n=this._getOrReturnCtx(e,n),q(n,{code:L.invalid_string,validation:{includes:i.value,position:i.position},message:i.message}),t.dirty()):i.kind==="toLowerCase"?e.data=e.data.toLowerCase():i.kind==="toUpperCase"?e.data=e.data.toUpperCase():i.kind==="startsWith"?e.data.startsWith(i.value)||(n=this._getOrReturnCtx(e,n),q(n,{code:L.invalid_string,validation:{startsWith:i.value},message:i.message}),t.dirty()):i.kind==="endsWith"?e.data.endsWith(i.value)||(n=this._getOrReturnCtx(e,n),q(n,{code:L.invalid_string,validation:{endsWith:i.value},message:i.message}),t.dirty()):i.kind==="datetime"?Oo(i).test(e.data)||(n=this._getOrReturnCtx(e,n),q(n,{code:L.invalid_string,validation:"datetime",message:i.message}),t.dirty()):i.kind==="date"?na.test(e.data)||(n=this._getOrReturnCtx(e,n),q(n,{code:L.invalid_string,validation:"date",message:i.message}),t.dirty()):i.kind==="time"?new RegExp(`^${Lo(i)}$`).test(e.data)||(n=this._getOrReturnCtx(e,n),q(n,{code:L.invalid_string,validation:"time",message:i.message}),t.dirty()):i.kind==="duration"?Ji.test(e.data)||(n=this._getOrReturnCtx(e,n),q(n,{validation:"duration",code:L.invalid_string,message:i.message}),t.dirty()):i.kind==="ip"?(s=e.data,((o=i.version)!=="v4"&&o||!Ki.test(s))&&(o!=="v6"&&o||!Xi.test(s))&&(n=this._getOrReturnCtx(e,n),q(n,{validation:"ip",code:L.invalid_string,message:i.message}),t.dirty())):i.kind==="jwt"?sa(e.data,i.alg)||(n=this._getOrReturnCtx(e,n),q(n,{validation:"jwt",code:L.invalid_string,message:i.message}),t.dirty()):i.kind==="cidr"?ra(e.data,i.version)||(n=this._getOrReturnCtx(e,n),q(n,{validation:"cidr",code:L.invalid_string,message:i.message}),t.dirty()):i.kind==="base64"?ea.test(e.data)||(n=this._getOrReturnCtx(e,n),q(n,{validation:"base64",code:L.invalid_string,message:i.message}),t.dirty()):i.kind==="base64url"?ta.test(e.data)||(n=this._getOrReturnCtx(e,n),q(n,{validation:"base64url",code:L.invalid_string,message:i.message}),t.dirty()):me.assertNever(i);var s,o;return{status:t.value,value:e.data}}_regex(e,t,n){return this.refinement(s=>e.test(s),{validation:t,code:L.invalid_string,...K.errToObj(n)})}_addCheck(e){return new He({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...K.errToObj(e)})}url(e){return this._addCheck({kind:"url",...K.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...K.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...K.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...K.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...K.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...K.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...K.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...K.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...K.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...K.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...K.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...K.errToObj(e)})}datetime(e){var t,n;return typeof e=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:(e==null?void 0:e.precision)===void 0?null:e==null?void 0:e.precision,offset:(t=e==null?void 0:e.offset)!==null&&t!==void 0&&t,local:(n=e==null?void 0:e.local)!==null&&n!==void 0&&n,...K.errToObj(e==null?void 0:e.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return typeof e=="string"?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:(e==null?void 0:e.precision)===void 0?null:e==null?void 0:e.precision,...K.errToObj(e==null?void 0:e.message)})}duration(e){return this._addCheck({kind:"duration",...K.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...K.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t==null?void 0:t.position,...K.errToObj(t==null?void 0:t.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...K.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...K.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...K.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...K.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...K.errToObj(t)})}nonempty(e){return this.min(1,K.errToObj(e))}trim(){return new He({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new He({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new He({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>e.kind==="datetime")}get isDate(){return!!this._def.checks.find(e=>e.kind==="date")}get isTime(){return!!this._def.checks.find(e=>e.kind==="time")}get isDuration(){return!!this._def.checks.find(e=>e.kind==="duration")}get isEmail(){return!!this._def.checks.find(e=>e.kind==="email")}get isURL(){return!!this._def.checks.find(e=>e.kind==="url")}get isEmoji(){return!!this._def.checks.find(e=>e.kind==="emoji")}get isUUID(){return!!this._def.checks.find(e=>e.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(e=>e.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(e=>e.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(e=>e.kind==="cuid2")}get isULID(){return!!this._def.checks.find(e=>e.kind==="ulid")}get isIP(){return!!this._def.checks.find(e=>e.kind==="ip")}get isCIDR(){return!!this._def.checks.find(e=>e.kind==="cidr")}get isBase64(){return!!this._def.checks.find(e=>e.kind==="base64")}get isBase64url(){return!!this._def.checks.find(e=>e.kind==="base64url")}get minLength(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}}function oa(r,e){const t=(r.toString().split(".")[1]||"").length,n=(e.toString().split(".")[1]||"").length,s=t>n?t:n;return parseInt(r.toFixed(s).replace(".",""))%parseInt(e.toFixed(s).replace(".",""))/Math.pow(10,s)}He.create=r=>{var e;return new He({checks:[],typeName:ee.ZodString,coerce:(e=r==null?void 0:r.coerce)!==null&&e!==void 0&&e,...se(r)})};class gt extends re{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==B.number){const s=this._getOrReturnCtx(e);return q(s,{code:L.invalid_type,expected:B.number,received:s.parsedType}),te}let t;const n=new Re;for(const s of this._def.checks)s.kind==="int"?me.isInteger(e.data)||(t=this._getOrReturnCtx(e,t),q(t,{code:L.invalid_type,expected:"integer",received:"float",message:s.message}),n.dirty()):s.kind==="min"?(s.inclusive?e.data<s.value:e.data<=s.value)&&(t=this._getOrReturnCtx(e,t),q(t,{code:L.too_small,minimum:s.value,type:"number",inclusive:s.inclusive,exact:!1,message:s.message}),n.dirty()):s.kind==="max"?(s.inclusive?e.data>s.value:e.data>=s.value)&&(t=this._getOrReturnCtx(e,t),q(t,{code:L.too_big,maximum:s.value,type:"number",inclusive:s.inclusive,exact:!1,message:s.message}),n.dirty()):s.kind==="multipleOf"?oa(e.data,s.value)!==0&&(t=this._getOrReturnCtx(e,t),q(t,{code:L.not_multiple_of,multipleOf:s.value,message:s.message}),n.dirty()):s.kind==="finite"?Number.isFinite(e.data)||(t=this._getOrReturnCtx(e,t),q(t,{code:L.not_finite,message:s.message}),n.dirty()):me.assertNever(s);return{status:n.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,K.toString(t))}gt(e,t){return this.setLimit("min",e,!1,K.toString(t))}lte(e,t){return this.setLimit("max",e,!0,K.toString(t))}lt(e,t){return this.setLimit("max",e,!1,K.toString(t))}setLimit(e,t,n,s){return new gt({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:K.toString(s)}]})}_addCheck(e){return new gt({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:K.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:K.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:K.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:K.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:K.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:K.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:K.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:K.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:K.toString(e)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>e.kind==="int"||e.kind==="multipleOf"&&me.isInteger(e.value))}get isFinite(){let e=null,t=null;for(const n of this._def.checks){if(n.kind==="finite"||n.kind==="int"||n.kind==="multipleOf")return!0;n.kind==="min"?(t===null||n.value>t)&&(t=n.value):n.kind==="max"&&(e===null||n.value<e)&&(e=n.value)}return Number.isFinite(t)&&Number.isFinite(e)}}gt.create=r=>new gt({checks:[],typeName:ee.ZodNumber,coerce:(r==null?void 0:r.coerce)||!1,...se(r)});class $t extends re{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==B.bigint)return this._getInvalidInput(e);let t;const n=new Re;for(const s of this._def.checks)s.kind==="min"?(s.inclusive?e.data<s.value:e.data<=s.value)&&(t=this._getOrReturnCtx(e,t),q(t,{code:L.too_small,type:"bigint",minimum:s.value,inclusive:s.inclusive,message:s.message}),n.dirty()):s.kind==="max"?(s.inclusive?e.data>s.value:e.data>=s.value)&&(t=this._getOrReturnCtx(e,t),q(t,{code:L.too_big,type:"bigint",maximum:s.value,inclusive:s.inclusive,message:s.message}),n.dirty()):s.kind==="multipleOf"?e.data%s.value!==BigInt(0)&&(t=this._getOrReturnCtx(e,t),q(t,{code:L.not_multiple_of,multipleOf:s.value,message:s.message}),n.dirty()):me.assertNever(s);return{status:n.value,value:e.data}}_getInvalidInput(e){const t=this._getOrReturnCtx(e);return q(t,{code:L.invalid_type,expected:B.bigint,received:t.parsedType}),te}gte(e,t){return this.setLimit("min",e,!0,K.toString(t))}gt(e,t){return this.setLimit("min",e,!1,K.toString(t))}lte(e,t){return this.setLimit("max",e,!0,K.toString(t))}lt(e,t){return this.setLimit("max",e,!1,K.toString(t))}setLimit(e,t,n,s){return new $t({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:K.toString(s)}]})}_addCheck(e){return new $t({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:K.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:K.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:K.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:K.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:K.toString(t)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}}$t.create=r=>{var e;return new $t({checks:[],typeName:ee.ZodBigInt,coerce:(e=r==null?void 0:r.coerce)!==null&&e!==void 0&&e,...se(r)})};class Jt extends re{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==B.boolean){const t=this._getOrReturnCtx(e);return q(t,{code:L.invalid_type,expected:B.boolean,received:t.parsedType}),te}return Le(e.data)}}Jt.create=r=>new Jt({typeName:ee.ZodBoolean,coerce:(r==null?void 0:r.coerce)||!1,...se(r)});class Mt extends re{_parse(e){if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==B.date){const s=this._getOrReturnCtx(e);return q(s,{code:L.invalid_type,expected:B.date,received:s.parsedType}),te}if(isNaN(e.data.getTime()))return q(this._getOrReturnCtx(e),{code:L.invalid_date}),te;const t=new Re;let n;for(const s of this._def.checks)s.kind==="min"?e.data.getTime()<s.value&&(n=this._getOrReturnCtx(e,n),q(n,{code:L.too_small,message:s.message,inclusive:!0,exact:!1,minimum:s.value,type:"date"}),t.dirty()):s.kind==="max"?e.data.getTime()>s.value&&(n=this._getOrReturnCtx(e,n),q(n,{code:L.too_big,message:s.message,inclusive:!0,exact:!1,maximum:s.value,type:"date"}),t.dirty()):me.assertNever(s);return{status:t.value,value:new Date(e.data.getTime())}}_addCheck(e){return new Mt({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:K.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:K.toString(t)})}get minDate(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e!=null?new Date(e):null}get maxDate(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e!=null?new Date(e):null}}Mt.create=r=>new Mt({checks:[],coerce:(r==null?void 0:r.coerce)||!1,typeName:ee.ZodDate,...se(r)});class Sn extends re{_parse(e){if(this._getType(e)!==B.symbol){const t=this._getOrReturnCtx(e);return q(t,{code:L.invalid_type,expected:B.symbol,received:t.parsedType}),te}return Le(e.data)}}Sn.create=r=>new Sn({typeName:ee.ZodSymbol,...se(r)});class Wt extends re{_parse(e){if(this._getType(e)!==B.undefined){const t=this._getOrReturnCtx(e);return q(t,{code:L.invalid_type,expected:B.undefined,received:t.parsedType}),te}return Le(e.data)}}Wt.create=r=>new Wt({typeName:ee.ZodUndefined,...se(r)});class Kt extends re{_parse(e){if(this._getType(e)!==B.null){const t=this._getOrReturnCtx(e);return q(t,{code:L.invalid_type,expected:B.null,received:t.parsedType}),te}return Le(e.data)}}Kt.create=r=>new Kt({typeName:ee.ZodNull,...se(r)});class Ot extends re{constructor(){super(...arguments),this._any=!0}_parse(e){return Le(e.data)}}Ot.create=r=>new Ot({typeName:ee.ZodAny,...se(r)});class Ct extends re{constructor(){super(...arguments),this._unknown=!0}_parse(e){return Le(e.data)}}Ct.create=r=>new Ct({typeName:ee.ZodUnknown,...se(r)});class ot extends re{_parse(e){const t=this._getOrReturnCtx(e);return q(t,{code:L.invalid_type,expected:B.never,received:t.parsedType}),te}}ot.create=r=>new ot({typeName:ee.ZodNever,...se(r)});class xn extends re{_parse(e){if(this._getType(e)!==B.undefined){const t=this._getOrReturnCtx(e);return q(t,{code:L.invalid_type,expected:B.void,received:t.parsedType}),te}return Le(e.data)}}xn.create=r=>new xn({typeName:ee.ZodVoid,...se(r)});class Ge extends re{_parse(e){const{ctx:t,status:n}=this._processInputParams(e),s=this._def;if(t.parsedType!==B.array)return q(t,{code:L.invalid_type,expected:B.array,received:t.parsedType}),te;if(s.exactLength!==null){const i=t.data.length>s.exactLength.value,l=t.data.length<s.exactLength.value;(i||l)&&(q(t,{code:i?L.too_big:L.too_small,minimum:l?s.exactLength.value:void 0,maximum:i?s.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:s.exactLength.message}),n.dirty())}if(s.minLength!==null&&t.data.length<s.minLength.value&&(q(t,{code:L.too_small,minimum:s.minLength.value,type:"array",inclusive:!0,exact:!1,message:s.minLength.message}),n.dirty()),s.maxLength!==null&&t.data.length>s.maxLength.value&&(q(t,{code:L.too_big,maximum:s.maxLength.value,type:"array",inclusive:!0,exact:!1,message:s.maxLength.message}),n.dirty()),t.common.async)return Promise.all([...t.data].map((i,l)=>s.type._parseAsync(new Xe(t,i,t.path,l)))).then(i=>Re.mergeArray(n,i));const o=[...t.data].map((i,l)=>s.type._parseSync(new Xe(t,i,t.path,l)));return Re.mergeArray(n,o)}get element(){return this._def.type}min(e,t){return new Ge({...this._def,minLength:{value:e,message:K.toString(t)}})}max(e,t){return new Ge({...this._def,maxLength:{value:e,message:K.toString(t)}})}length(e,t){return new Ge({...this._def,exactLength:{value:e,message:K.toString(t)}})}nonempty(e){return this.min(1,e)}}function Et(r){if(r instanceof Se){const e={};for(const t in r.shape){const n=r.shape[t];e[t]=Je.create(Et(n))}return new Se({...r._def,shape:()=>e})}return r instanceof Ge?new Ge({...r._def,type:Et(r.element)}):r instanceof Je?Je.create(Et(r.unwrap())):r instanceof vt?vt.create(Et(r.unwrap())):r instanceof Qe?Qe.create(r.items.map(e=>Et(e))):r}Ge.create=(r,e)=>new Ge({type:r,minLength:null,maxLength:null,exactLength:null,typeName:ee.ZodArray,...se(e)});class Se extends re{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const e=this._def.shape(),t=me.objectKeys(e);return this._cached={shape:e,keys:t}}_parse(e){if(this._getType(e)!==B.object){const a=this._getOrReturnCtx(e);return q(a,{code:L.invalid_type,expected:B.object,received:a.parsedType}),te}const{status:t,ctx:n}=this._processInputParams(e),{shape:s,keys:o}=this._getCached(),i=[];if(!(this._def.catchall instanceof ot&&this._def.unknownKeys==="strip"))for(const a in n.data)o.includes(a)||i.push(a);const l=[];for(const a of o){const c=s[a],d=n.data[a];l.push({key:{status:"valid",value:a},value:c._parse(new Xe(n,d,n.path,a)),alwaysSet:a in n.data})}if(this._def.catchall instanceof ot){const a=this._def.unknownKeys;if(a==="passthrough")for(const c of i)l.push({key:{status:"valid",value:c},value:{status:"valid",value:n.data[c]}});else if(a==="strict")i.length>0&&(q(n,{code:L.unrecognized_keys,keys:i}),t.dirty());else if(a!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const a=this._def.catchall;for(const c of i){const d=n.data[c];l.push({key:{status:"valid",value:c},value:a._parse(new Xe(n,d,n.path,c)),alwaysSet:c in n.data})}}return n.common.async?Promise.resolve().then(async()=>{const a=[];for(const c of l){const d=await c.key,u=await c.value;a.push({key:d,value:u,alwaysSet:c.alwaysSet})}return a}).then(a=>Re.mergeObjectSync(t,a)):Re.mergeObjectSync(t,l)}get shape(){return this._def.shape()}strict(e){return K.errToObj,new Se({...this._def,unknownKeys:"strict",...e!==void 0?{errorMap:(t,n)=>{var s,o,i,l;const a=(i=(o=(s=this._def).errorMap)===null||o===void 0?void 0:o.call(s,t,n).message)!==null&&i!==void 0?i:n.defaultError;return t.code==="unrecognized_keys"?{message:(l=K.errToObj(e).message)!==null&&l!==void 0?l:a}:{message:a}}}:{}})}strip(){return new Se({...this._def,unknownKeys:"strip"})}passthrough(){return new Se({...this._def,unknownKeys:"passthrough"})}extend(e){return new Se({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new Se({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:ee.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new Se({...this._def,catchall:e})}pick(e){const t={};return me.objectKeys(e).forEach(n=>{e[n]&&this.shape[n]&&(t[n]=this.shape[n])}),new Se({...this._def,shape:()=>t})}omit(e){const t={};return me.objectKeys(this.shape).forEach(n=>{e[n]||(t[n]=this.shape[n])}),new Se({...this._def,shape:()=>t})}deepPartial(){return Et(this)}partial(e){const t={};return me.objectKeys(this.shape).forEach(n=>{const s=this.shape[n];e&&!e[n]?t[n]=s:t[n]=s.optional()}),new Se({...this._def,shape:()=>t})}required(e){const t={};return me.objectKeys(this.shape).forEach(n=>{if(e&&!e[n])t[n]=this.shape[n];else{let s=this.shape[n];for(;s instanceof Je;)s=s._def.innerType;t[n]=s}}),new Se({...this._def,shape:()=>t})}keyof(){return zo(me.objectKeys(this.shape))}}Se.create=(r,e)=>new Se({shape:()=>r,unknownKeys:"strip",catchall:ot.create(),typeName:ee.ZodObject,...se(e)}),Se.strictCreate=(r,e)=>new Se({shape:()=>r,unknownKeys:"strict",catchall:ot.create(),typeName:ee.ZodObject,...se(e)}),Se.lazycreate=(r,e)=>new Se({shape:r,unknownKeys:"strip",catchall:ot.create(),typeName:ee.ZodObject,...se(e)});class Yt extends re{_parse(e){const{ctx:t}=this._processInputParams(e),n=this._def.options;if(t.common.async)return Promise.all(n.map(async s=>{const o={...t,common:{...t.common,issues:[]},parent:null};return{result:await s._parseAsync({data:t.data,path:t.path,parent:o}),ctx:o}})).then(function(s){for(const i of s)if(i.result.status==="valid")return i.result;for(const i of s)if(i.result.status==="dirty")return t.common.issues.push(...i.ctx.common.issues),i.result;const o=s.map(i=>new Ue(i.ctx.common.issues));return q(t,{code:L.invalid_union,unionErrors:o}),te});{let s;const o=[];for(const l of n){const a={...t,common:{...t.common,issues:[]},parent:null},c=l._parseSync({data:t.data,path:t.path,parent:a});if(c.status==="valid")return c;c.status!=="dirty"||s||(s={result:c,ctx:a}),a.common.issues.length&&o.push(a.common.issues)}if(s)return t.common.issues.push(...s.ctx.common.issues),s.result;const i=o.map(l=>new Ue(l));return q(t,{code:L.invalid_union,unionErrors:i}),te}}get options(){return this._def.options}}Yt.create=(r,e)=>new Yt({options:r,typeName:ee.ZodUnion,...se(e)});const at=r=>r instanceof Qt?at(r.schema):r instanceof Be?at(r.innerType()):r instanceof en?[r.value]:r instanceof ht?r.options:r instanceof tn?me.objectValues(r.enum):r instanceof nn?at(r._def.innerType):r instanceof Wt?[void 0]:r instanceof Kt?[null]:r instanceof Je?[void 0,...at(r.unwrap())]:r instanceof vt?[null,...at(r.unwrap())]:r instanceof os||r instanceof rn?at(r.unwrap()):r instanceof sn?at(r._def.innerType):[];class Rn extends re{_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==B.object)return q(t,{code:L.invalid_type,expected:B.object,received:t.parsedType}),te;const n=this.discriminator,s=t.data[n],o=this.optionsMap.get(s);return o?t.common.async?o._parseAsync({data:t.data,path:t.path,parent:t}):o._parseSync({data:t.data,path:t.path,parent:t}):(q(t,{code:L.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[n]}),te)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,n){const s=new Map;for(const o of t){const i=at(o.shape[e]);if(!i.length)throw new Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(const l of i){if(s.has(l))throw new Error(`Discriminator property ${String(e)} has duplicate value ${String(l)}`);s.set(l,o)}}return new Rn({typeName:ee.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:s,...se(n)})}}function Wn(r,e){const t=et(r),n=et(e);if(r===e)return{valid:!0,data:r};if(t===B.object&&n===B.object){const s=me.objectKeys(e),o=me.objectKeys(r).filter(l=>s.indexOf(l)!==-1),i={...r,...e};for(const l of o){const a=Wn(r[l],e[l]);if(!a.valid)return{valid:!1};i[l]=a.data}return{valid:!0,data:i}}if(t===B.array&&n===B.array){if(r.length!==e.length)return{valid:!1};const s=[];for(let o=0;o<r.length;o++){const i=Wn(r[o],e[o]);if(!i.valid)return{valid:!1};s.push(i.data)}return{valid:!0,data:s}}return t===B.date&&n===B.date&&+r==+e?{valid:!0,data:r}:{valid:!1}}class Xt extends re{_parse(e){const{status:t,ctx:n}=this._processInputParams(e),s=(o,i)=>{if(Gn(o)||Gn(i))return te;const l=Wn(o.value,i.value);return l.valid?((Jn(o)||Jn(i))&&t.dirty(),{status:t.value,value:l.data}):(q(n,{code:L.invalid_intersection_types}),te)};return n.common.async?Promise.all([this._def.left._parseAsync({data:n.data,path:n.path,parent:n}),this._def.right._parseAsync({data:n.data,path:n.path,parent:n})]).then(([o,i])=>s(o,i)):s(this._def.left._parseSync({data:n.data,path:n.path,parent:n}),this._def.right._parseSync({data:n.data,path:n.path,parent:n}))}}Xt.create=(r,e,t)=>new Xt({left:r,right:e,typeName:ee.ZodIntersection,...se(t)});class Qe extends re{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==B.array)return q(n,{code:L.invalid_type,expected:B.array,received:n.parsedType}),te;if(n.data.length<this._def.items.length)return q(n,{code:L.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),te;!this._def.rest&&n.data.length>this._def.items.length&&(q(n,{code:L.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());const s=[...n.data].map((o,i)=>{const l=this._def.items[i]||this._def.rest;return l?l._parse(new Xe(n,o,n.path,i)):null}).filter(o=>!!o);return n.common.async?Promise.all(s).then(o=>Re.mergeArray(t,o)):Re.mergeArray(t,s)}get items(){return this._def.items}rest(e){return new Qe({...this._def,rest:e})}}Qe.create=(r,e)=>{if(!Array.isArray(r))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new Qe({items:r,typeName:ee.ZodTuple,rest:null,...se(e)})};class Pn extends re{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==B.object)return q(n,{code:L.invalid_type,expected:B.object,received:n.parsedType}),te;const s=[],o=this._def.keyType,i=this._def.valueType;for(const l in n.data)s.push({key:o._parse(new Xe(n,l,n.path,l)),value:i._parse(new Xe(n,n.data[l],n.path,l)),alwaysSet:l in n.data});return n.common.async?Re.mergeObjectAsync(t,s):Re.mergeObjectSync(t,s)}get element(){return this._def.valueType}static create(e,t,n){return new Pn(t instanceof re?{keyType:e,valueType:t,typeName:ee.ZodRecord,...se(n)}:{keyType:He.create(),valueType:e,typeName:ee.ZodRecord,...se(t)})}}class Cn extends re{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==B.map)return q(n,{code:L.invalid_type,expected:B.map,received:n.parsedType}),te;const s=this._def.keyType,o=this._def.valueType,i=[...n.data.entries()].map(([l,a],c)=>({key:s._parse(new Xe(n,l,n.path,[c,"key"])),value:o._parse(new Xe(n,a,n.path,[c,"value"]))}));if(n.common.async){const l=new Map;return Promise.resolve().then(async()=>{for(const a of i){const c=await a.key,d=await a.value;if(c.status==="aborted"||d.status==="aborted")return te;c.status!=="dirty"&&d.status!=="dirty"||t.dirty(),l.set(c.value,d.value)}return{status:t.value,value:l}})}{const l=new Map;for(const a of i){const c=a.key,d=a.value;if(c.status==="aborted"||d.status==="aborted")return te;c.status!=="dirty"&&d.status!=="dirty"||t.dirty(),l.set(c.value,d.value)}return{status:t.value,value:l}}}}Cn.create=(r,e,t)=>new Cn({valueType:e,keyType:r,typeName:ee.ZodMap,...se(t)});class At extends re{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==B.set)return q(n,{code:L.invalid_type,expected:B.set,received:n.parsedType}),te;const s=this._def;s.minSize!==null&&n.data.size<s.minSize.value&&(q(n,{code:L.too_small,minimum:s.minSize.value,type:"set",inclusive:!0,exact:!1,message:s.minSize.message}),t.dirty()),s.maxSize!==null&&n.data.size>s.maxSize.value&&(q(n,{code:L.too_big,maximum:s.maxSize.value,type:"set",inclusive:!0,exact:!1,message:s.maxSize.message}),t.dirty());const o=this._def.valueType;function i(a){const c=new Set;for(const d of a){if(d.status==="aborted")return te;d.status==="dirty"&&t.dirty(),c.add(d.value)}return{status:t.value,value:c}}const l=[...n.data.values()].map((a,c)=>o._parse(new Xe(n,a,n.path,c)));return n.common.async?Promise.all(l).then(a=>i(a)):i(l)}min(e,t){return new At({...this._def,minSize:{value:e,message:K.toString(t)}})}max(e,t){return new At({...this._def,maxSize:{value:e,message:K.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}At.create=(r,e)=>new At({valueType:r,minSize:null,maxSize:null,typeName:ee.ZodSet,...se(e)});class Pt extends re{constructor(){super(...arguments),this.validate=this.implement}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==B.function)return q(t,{code:L.invalid_type,expected:B.function,received:t.parsedType}),te;function n(l,a){return vn({data:l,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,hn(),Lt].filter(c=>!!c),issueData:{code:L.invalid_arguments,argumentsError:a}})}function s(l,a){return vn({data:l,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,hn(),Lt].filter(c=>!!c),issueData:{code:L.invalid_return_type,returnTypeError:a}})}const o={errorMap:t.common.contextualErrorMap},i=t.data;if(this._def.returns instanceof zt){const l=this;return Le(async function(...a){const c=new Ue([]),d=await l._def.args.parseAsync(a,o).catch(f=>{throw c.addIssue(n(a,f)),c}),u=await Reflect.apply(i,this,d);return await l._def.returns._def.type.parseAsync(u,o).catch(f=>{throw c.addIssue(s(u,f)),c})})}{const l=this;return Le(function(...a){const c=l._def.args.safeParse(a,o);if(!c.success)throw new Ue([n(a,c.error)]);const d=Reflect.apply(i,this,c.data),u=l._def.returns.safeParse(d,o);if(!u.success)throw new Ue([s(d,u.error)]);return u.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new Pt({...this._def,args:Qe.create(e).rest(Ct.create())})}returns(e){return new Pt({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,n){return new Pt({args:e||Qe.create([]).rest(Ct.create()),returns:t||Ct.create(),typeName:ee.ZodFunction,...se(n)})}}class Qt extends re{get schema(){return this._def.getter()}_parse(e){const{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}Qt.create=(r,e)=>new Qt({getter:r,typeName:ee.ZodLazy,...se(e)});class en extends re{_parse(e){if(e.data!==this._def.value){const t=this._getOrReturnCtx(e);return q(t,{received:t.data,code:L.invalid_literal,expected:this._def.value}),te}return{status:"valid",value:e.data}}get value(){return this._def.value}}function zo(r,e){return new ht({values:r,typeName:ee.ZodEnum,...se(e)})}en.create=(r,e)=>new en({value:r,typeName:ee.ZodLiteral,...se(e)});class ht extends re{constructor(){super(...arguments),qt.set(this,void 0)}_parse(e){if(typeof e.data!="string"){const t=this._getOrReturnCtx(e),n=this._def.values;return q(t,{expected:me.joinValues(n),received:t.parsedType,code:L.invalid_type}),te}if(wn(this,qt)||Ro(this,qt,new Set(this._def.values)),!wn(this,qt).has(e.data)){const t=this._getOrReturnCtx(e),n=this._def.values;return q(t,{received:t.data,code:L.invalid_enum_value,options:n}),te}return Le(e.data)}get options(){return this._def.values}get enum(){const e={};for(const t of this._def.values)e[t]=t;return e}get Values(){const e={};for(const t of this._def.values)e[t]=t;return e}get Enum(){const e={};for(const t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return ht.create(e,{...this._def,...t})}exclude(e,t=this._def){return ht.create(this.options.filter(n=>!e.includes(n)),{...this._def,...t})}}qt=new WeakMap,ht.create=zo;class tn extends re{constructor(){super(...arguments),Ht.set(this,void 0)}_parse(e){const t=me.getValidEnumValues(this._def.values),n=this._getOrReturnCtx(e);if(n.parsedType!==B.string&&n.parsedType!==B.number){const s=me.objectValues(t);return q(n,{expected:me.joinValues(s),received:n.parsedType,code:L.invalid_type}),te}if(wn(this,Ht)||Ro(this,Ht,new Set(me.getValidEnumValues(this._def.values))),!wn(this,Ht).has(e.data)){const s=me.objectValues(t);return q(n,{received:n.data,code:L.invalid_enum_value,options:s}),te}return Le(e.data)}get enum(){return this._def.values}}Ht=new WeakMap,tn.create=(r,e)=>new tn({values:r,typeName:ee.ZodNativeEnum,...se(e)});class zt extends re{unwrap(){return this._def.type}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==B.promise&&t.common.async===!1)return q(t,{code:L.invalid_type,expected:B.promise,received:t.parsedType}),te;const n=t.parsedType===B.promise?t.data:Promise.resolve(t.data);return Le(n.then(s=>this._def.type.parseAsync(s,{path:t.path,errorMap:t.common.contextualErrorMap})))}}zt.create=(r,e)=>new zt({type:r,typeName:ee.ZodPromise,...se(e)});class Be extends re{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===ee.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){const{status:t,ctx:n}=this._processInputParams(e),s=this._def.effect||null,o={addIssue:i=>{q(n,i),i.fatal?t.abort():t.dirty()},get path(){return n.path}};if(o.addIssue=o.addIssue.bind(o),s.type==="preprocess"){const i=s.transform(n.data,o);if(n.common.async)return Promise.resolve(i).then(async l=>{if(t.value==="aborted")return te;const a=await this._def.schema._parseAsync({data:l,path:n.path,parent:n});return a.status==="aborted"?te:a.status==="dirty"||t.value==="dirty"?yn(a.value):a});{if(t.value==="aborted")return te;const l=this._def.schema._parseSync({data:i,path:n.path,parent:n});return l.status==="aborted"?te:l.status==="dirty"||t.value==="dirty"?yn(l.value):l}}if(s.type==="refinement"){const i=l=>{const a=s.refinement(l,o);if(n.common.async)return Promise.resolve(a);if(a instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return l};if(n.common.async===!1){const l=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});return l.status==="aborted"?te:(l.status==="dirty"&&t.dirty(),i(l.value),{status:t.value,value:l.value})}return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(l=>l.status==="aborted"?te:(l.status==="dirty"&&t.dirty(),i(l.value).then(()=>({status:t.value,value:l.value}))))}if(s.type==="transform"){if(n.common.async===!1){const i=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});if(!kt(i))return i;const l=s.transform(i.value,o);if(l instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:l}}return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(i=>kt(i)?Promise.resolve(s.transform(i.value,o)).then(l=>({status:t.value,value:l})):i)}me.assertNever(s)}}Be.create=(r,e,t)=>new Be({schema:r,typeName:ee.ZodEffects,effect:e,...se(t)}),Be.createWithPreprocess=(r,e,t)=>new Be({schema:e,effect:{type:"preprocess",transform:r},typeName:ee.ZodEffects,...se(t)});class Je extends re{_parse(e){return this._getType(e)===B.undefined?Le(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}Je.create=(r,e)=>new Je({innerType:r,typeName:ee.ZodOptional,...se(e)});class vt extends re{_parse(e){return this._getType(e)===B.null?Le(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}vt.create=(r,e)=>new vt({innerType:r,typeName:ee.ZodNullable,...se(e)});class nn extends re{_parse(e){const{ctx:t}=this._processInputParams(e);let n=t.data;return t.parsedType===B.undefined&&(n=this._def.defaultValue()),this._def.innerType._parse({data:n,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}nn.create=(r,e)=>new nn({innerType:r,typeName:ee.ZodDefault,defaultValue:typeof e.default=="function"?e.default:()=>e.default,...se(e)});class sn extends re{_parse(e){const{ctx:t}=this._processInputParams(e),n={...t,common:{...t.common,issues:[]}},s=this._def.innerType._parse({data:n.data,path:n.path,parent:{...n}});return Gt(s)?s.then(o=>({status:"valid",value:o.status==="valid"?o.value:this._def.catchValue({get error(){return new Ue(n.common.issues)},input:n.data})})):{status:"valid",value:s.status==="valid"?s.value:this._def.catchValue({get error(){return new Ue(n.common.issues)},input:n.data})}}removeCatch(){return this._def.innerType}}sn.create=(r,e)=>new sn({innerType:r,typeName:ee.ZodCatch,catchValue:typeof e.catch=="function"?e.catch:()=>e.catch,...se(e)});class _n extends re{_parse(e){if(this._getType(e)!==B.nan){const t=this._getOrReturnCtx(e);return q(t,{code:L.invalid_type,expected:B.nan,received:t.parsedType}),te}return{status:"valid",value:e.data}}}_n.create=r=>new _n({typeName:ee.ZodNaN,...se(r)});const ia=Symbol("zod_brand");class os extends re{_parse(e){const{ctx:t}=this._processInputParams(e),n=t.data;return this._def.type._parse({data:n,path:t.path,parent:t})}unwrap(){return this._def.type}}class cn extends re{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.common.async)return(async()=>{const s=await this._def.in._parseAsync({data:n.data,path:n.path,parent:n});return s.status==="aborted"?te:s.status==="dirty"?(t.dirty(),yn(s.value)):this._def.out._parseAsync({data:s.value,path:n.path,parent:n})})();{const s=this._def.in._parseSync({data:n.data,path:n.path,parent:n});return s.status==="aborted"?te:s.status==="dirty"?(t.dirty(),{status:"dirty",value:s.value}):this._def.out._parseSync({data:s.value,path:n.path,parent:n})}}static create(e,t){return new cn({in:e,out:t,typeName:ee.ZodPipeline})}}class rn extends re{_parse(e){const t=this._def.innerType._parse(e),n=s=>(kt(s)&&(s.value=Object.freeze(s.value)),s);return Gt(t)?t.then(s=>n(s)):n(t)}unwrap(){return this._def.innerType}}function xs(r,e={},t){return r?Ot.create().superRefine((n,s)=>{var o,i;if(!r(n)){const l=typeof e=="function"?e(n):typeof e=="string"?{message:e}:e,a=(i=(o=l.fatal)!==null&&o!==void 0?o:t)===null||i===void 0||i,c=typeof l=="string"?{message:l}:l;s.addIssue({code:"custom",...c,fatal:a})}}):Ot.create()}rn.create=(r,e)=>new rn({innerType:r,typeName:ee.ZodReadonly,...se(e)});const aa={object:Se.lazycreate};var ee;(function(r){r.ZodString="ZodString",r.ZodNumber="ZodNumber",r.ZodNaN="ZodNaN",r.ZodBigInt="ZodBigInt",r.ZodBoolean="ZodBoolean",r.ZodDate="ZodDate",r.ZodSymbol="ZodSymbol",r.ZodUndefined="ZodUndefined",r.ZodNull="ZodNull",r.ZodAny="ZodAny",r.ZodUnknown="ZodUnknown",r.ZodNever="ZodNever",r.ZodVoid="ZodVoid",r.ZodArray="ZodArray",r.ZodObject="ZodObject",r.ZodUnion="ZodUnion",r.ZodDiscriminatedUnion="ZodDiscriminatedUnion",r.ZodIntersection="ZodIntersection",r.ZodTuple="ZodTuple",r.ZodRecord="ZodRecord",r.ZodMap="ZodMap",r.ZodSet="ZodSet",r.ZodFunction="ZodFunction",r.ZodLazy="ZodLazy",r.ZodLiteral="ZodLiteral",r.ZodEnum="ZodEnum",r.ZodEffects="ZodEffects",r.ZodNativeEnum="ZodNativeEnum",r.ZodOptional="ZodOptional",r.ZodNullable="ZodNullable",r.ZodDefault="ZodDefault",r.ZodCatch="ZodCatch",r.ZodPromise="ZodPromise",r.ZodBranded="ZodBranded",r.ZodPipeline="ZodPipeline",r.ZodReadonly="ZodReadonly"})(ee||(ee={}));const Cs=He.create,_s=gt.create,la=_n.create,ca=$t.create,bs=Jt.create,da=Mt.create,ua=Sn.create,pa=Wt.create,ma=Kt.create,fa=Ot.create,ga=Ct.create,$a=ot.create,ha=xn.create,va=Ge.create,ya=Se.create,wa=Se.strictCreate,Sa=Yt.create,xa=Rn.create,Ca=Xt.create,_a=Qe.create,ba=Pn.create,ka=Cn.create,Ma=At.create,Aa=Pt.create,Na=Qt.create,Ta=en.create,Ea=ht.create,Ia=tn.create,Ra=zt.create,ks=Be.create,Pa=Je.create,La=vt.create,Oa=Be.createWithPreprocess,za=cn.create,Fa={string:r=>He.create({...r,coerce:!0}),number:r=>gt.create({...r,coerce:!0}),boolean:r=>Jt.create({...r,coerce:!0}),bigint:r=>$t.create({...r,coerce:!0}),date:r=>Mt.create({...r,coerce:!0})},Za=te;var ge=Object.freeze({__proto__:null,defaultErrorMap:Lt,setErrorMap:function(r){Io=r},getErrorMap:hn,makeIssue:vn,EMPTY_PATH:[],addIssueToContext:q,ParseStatus:Re,INVALID:te,DIRTY:yn,OK:Le,isAborted:Gn,isDirty:Jn,isValid:kt,isAsync:Gt,get util(){return me},get objectUtil(){return Bn},ZodParsedType:B,getParsedType:et,ZodType:re,datetimeRegex:Oo,ZodString:He,ZodNumber:gt,ZodBigInt:$t,ZodBoolean:Jt,ZodDate:Mt,ZodSymbol:Sn,ZodUndefined:Wt,ZodNull:Kt,ZodAny:Ot,ZodUnknown:Ct,ZodNever:ot,ZodVoid:xn,ZodArray:Ge,ZodObject:Se,ZodUnion:Yt,ZodDiscriminatedUnion:Rn,ZodIntersection:Xt,ZodTuple:Qe,ZodRecord:Pn,ZodMap:Cn,ZodSet:At,ZodFunction:Pt,ZodLazy:Qt,ZodLiteral:en,ZodEnum:ht,ZodNativeEnum:tn,ZodPromise:zt,ZodEffects:Be,ZodTransformer:Be,ZodOptional:Je,ZodNullable:vt,ZodDefault:nn,ZodCatch:sn,ZodNaN:_n,BRAND:ia,ZodBranded:os,ZodPipeline:cn,ZodReadonly:rn,custom:xs,Schema:re,ZodSchema:re,late:aa,get ZodFirstPartyTypeKind(){return ee},coerce:Fa,any:fa,array:va,bigint:ca,boolean:bs,date:da,discriminatedUnion:xa,effect:ks,enum:Ea,function:Aa,instanceof:(r,e={message:`Input not instance of ${r.name}`})=>xs(t=>t instanceof r,e),intersection:Ca,lazy:Na,literal:Ta,map:ka,nan:la,nativeEnum:Ia,never:$a,null:ma,nullable:La,number:_s,object:ya,oboolean:()=>bs().optional(),onumber:()=>_s().optional(),optional:Pa,ostring:()=>Cs().optional(),pipeline:za,preprocess:Oa,promise:Ra,record:ba,set:Ma,strictObject:wa,string:Cs,symbol:ua,transformer:ks,tuple:_a,undefined:pa,union:Sa,unknown:ga,void:ha,NEVER:Za,ZodIssueCode:L,quotelessJson:r=>JSON.stringify(r,null,2).replace(/"([^"]+)":/g,"$1:"),ZodError:Ue});class Ce extends Error{constructor(e){super(e),this.name="MCPServerError",Object.setPrototypeOf(this,Ce.prototype)}}const tt=ge.object({name:ge.string().optional(),title:ge.string().optional(),type:ge.enum(["stdio","http","sse"]).optional(),command:ge.string().optional(),args:ge.array(ge.union([ge.string(),ge.number(),ge.boolean()])).optional(),env:ge.record(ge.union([ge.string(),ge.number(),ge.boolean(),ge.null(),ge.undefined()])).optional(),url:ge.string().optional()}).passthrough();function je(r){return(r==null?void 0:r.type)==="http"||(r==null?void 0:r.type)==="sse"}function It(r){return(r==null?void 0:r.type)==="stdio"}function bn(r){return je(r)?r.url:It(r)?r.command:""}const Ua=ge.array(tt),Da=ge.object({servers:ge.array(tt)}),ja=ge.object({mcpServers:ge.array(tt)}),Va=ge.object({servers:ge.record(ge.unknown())}),qa=ge.object({mcpServers:ge.record(ge.unknown())}),Ha=ge.record(ge.unknown()),Ba=tt.refine(r=>{const e=r.command!==void 0,t=r.url!==void 0;if(!e&&!t)return!1;const n=new Set(["name","title","type","command","args","env","url"]);return Object.keys(r).every(s=>n.has(s))},{message:"Single server object must have valid server properties"});function xt(r){try{const e=tt.transform(t=>{let n;if(t.type)n=t.type;else if(t.url)n="http";else{if(!t.command)throw new Error("Server must have either 'command' (for stdio) or 'url' (for http/sse) property");n="stdio"}if(n==="http"||n==="sse"){if(!t.url)throw new Error(`${n.toUpperCase()} server must have a 'url' property`);return{type:n,name:t.name||t.title||t.url,url:t.url}}{const s=t.command||"",o=t.args?t.args.map(c=>String(c)):[];if(!s)throw new Error("Stdio server must have a 'command' property");const i=o.length>0?`${s} ${o.join(" ")}`:s,l=t.name||t.title||(s?s.split(" ")[0]:""),a=t.env?Object.fromEntries(Object.entries(t.env).filter(([c,d])=>d!=null).map(([c,d])=>[c,String(d)])):void 0;return{type:"stdio",name:l,command:i,arguments:"",useShellInterpolation:!0,env:Object.keys(a||{}).length>0?a:void 0}}}).refine(t=>!!t.name,{message:"Server must have a name",path:["name"]}).refine(t=>t.type==="http"||t.type==="sse"?!!t.url:!!t.command,{message:"Server must have either 'command' (for stdio) or 'url' (for http/sse)",path:["command","url"]}).safeParse(r);if(!e.success)throw new Ce(e.error.message);return e.data}catch(e){throw e instanceof Error?new Ce(`Invalid server configuration: ${e.message}`):new Ce("Invalid server configuration")}}function Fo(r){try{const e=JSON.parse(r),t=ge.union([Ua.transform(n=>n.map(s=>xt(s))),Da.transform(n=>n.servers.map(s=>xt(s))),ja.transform(n=>n.mcpServers.map(s=>xt(s))),Va.transform(n=>Object.entries(n.servers).map(([s,o])=>{const i=tt.parse(o);return xt({...i,name:i.name||s})})),qa.transform(n=>Object.entries(n.mcpServers).map(([s,o])=>{const i=tt.parse(o);return xt({...i,name:i.name||s})})),Ba.transform(n=>[xt(n)]),Ha.transform(n=>{if(!Object.values(n).some(s=>{const o=tt.safeParse(s);return o.success&&(o.data.command!==void 0||o.data.url!==void 0)}))throw new Error("No command or url property found in any server config");return Object.entries(n).map(([s,o])=>{const i=tt.parse(o);return xt({...i,name:i.name||s})})})]).safeParse(e);if(t.success)return t.data;throw new Ce("Invalid JSON format. Expected an array of servers or an object with a 'servers' property.")}catch(e){throw e instanceof Ce?e:new Ce("Failed to parse MCP servers from JSON. Please check the format.")}}let Ga=class Zo{constructor(e){W(this,"servers",ye([]));this.host=e,this.loadServersFromStorage()}handleMessageFromExtension(e){const t=e.data;if(t.type===ue.getStoredMCPServersResponse){const n=t.data;return Array.isArray(n)&&this.servers.set(n),!0}return!1}async importServersFromJSON(e){return this.importFromJSON(e)}loadServersFromStorage(){try{this.host.postMessage({type:ue.getStoredMCPServers})}catch(e){console.error("Failed to load MCP servers:",e),this.servers.set([])}}saveServers(e){try{this.host.postMessage({type:ue.setStoredMCPServers,data:e})}catch(t){throw console.error("Failed to save MCP servers:",t),new Ce("Failed to save MCP servers")}}getServers(){return this.servers}addServer(e){this.checkExistingServerName(e.name),this.servers.update(t=>{const n=[...t,{...e,id:crypto.randomUUID()}];return this.saveServers(n),n})}addServers(e){for(const t of e)this.checkExistingServerName(t.name);this.servers.update(t=>{const n=[...t,...e.map(s=>({...s,id:crypto.randomUUID()}))];return this.saveServers(n),n})}checkExistingServerName(e,t){const n=dt(this.servers).find(s=>s.name===e);if(n&&(n==null?void 0:n.id)!==t)throw new Ce(`Server name '${e}' already exists`)}updateServer(e){this.checkExistingServerName(e.name,e.id),this.servers.update(t=>{const n=t.map(s=>s.id===e.id?e:s);return this.saveServers(n),n})}deleteServer(e){this.servers.update(t=>{const n=t.filter(s=>s.id!==e);return this.saveServers(n),n})}toggleDisabledServer(e){this.servers.update(t=>{const n=t.map(s=>s.id===e?{...s,disabled:!s.disabled}:s);return this.saveServers(n),n})}static convertServerToJSON(e){if(je(e))return JSON.stringify({mcpServers:{[e.name]:{url:e.url,type:e.type}}},null,2);{const t=e;return JSON.stringify({mcpServers:{[t.name]:{command:t.command.split(" ")[0],args:t.command.split(" ").slice(1),env:t.env}}},null,2)}}static parseServerValidationMessages(e){const t=new Map,n=new Map;e.forEach(o=>{var l,a;const i=(l=o.tools)==null?void 0:l.filter(c=>!c.enabled).map(c=>c.definition.mcp_tool_name);o.disabled?t.set(o.id,"MCP server has been manually disabled"):o.tools&&o.tools.length===0?t.set(o.id,"No tools are available for this MCP server"):i&&i.length===((a=o.tools)==null?void 0:a.length)?t.set(o.id,"All tools for this MCP server have validation errors: "+i.join(", ")):i&&i.length>0&&n.set(o.id,"MCP server has validation errors in the following tools which have been disabled: "+i.join(", "))});const s=this.parseDuplicateServerIds(e);return{errors:new Map([...t,...s]),warnings:n}}static parseDuplicateServerIds(e){const t=new Map;for(const s of e)t.has(s.name)||t.set(s.name,[]),t.get(s.name).push(s.id);const n=new Map;for(const[,s]of t)if(s.length>1)for(let o=1;o<s.length;o++)n.set(s[o],"MCP server is disabled due to duplicate server names");return n}static convertParsedServerToWebview(e){const{tools:t,...n}=e;return{...n,tools:void 0}}static parseServerConfigFromJSON(e){return Fo(e).map(t=>this.convertParsedServerToWebview(t))}importFromJSON(e){try{const t=Zo.parseServerConfigFromJSON(e),n=dt(this.servers),s=new Set(n.map(o=>o.name));for(const o of t){if(!o.name)throw new Ce("All servers must have a name.");if(s.has(o.name))throw new Ce(`A server with the name '${o.name}' already exists.`);s.add(o.name)}return this.servers.update(o=>{const i=[...o,...t.map(l=>({...l,id:crypto.randomUUID()}))];return this.saveServers(i),i}),t.length}catch(t){throw t instanceof Ce?t:new Ce("Failed to import MCP servers from JSON. Please check the format.")}}};class Ja{constructor(e){W(this,"_terminalSettings",ye({supportedShells:[],selectedShell:void 0,startupScript:void 0}));this._host=e,this.requestTerminalSettings()}handleMessageFromExtension(e){const t=e.data;return t.type===ue.terminalSettingsResponse&&(this._terminalSettings.set(t.data),!0)}getTerminalSettings(){return this._terminalSettings}requestTerminalSettings(){this._host.postMessage({type:ue.getTerminalSettings})}updateSelectedShell(e){this._terminalSettings.update(t=>({...t,selectedShell:e})),this._host.postMessage({type:ue.updateTerminalSettings,data:{selectedShell:e}})}updateStartupScript(e){this._terminalSettings.update(t=>({...t,startupScript:e})),this._host.postMessage({type:ue.updateTerminalSettings,data:{startupScript:e}})}}class Kn{constructor(e){W(this,"_soundSettings",ye(Zn));W(this,"_isLoaded",!1);W(this,"dispose",()=>{Un.disposeSounds()});this._msgBroker=e,this.initialize()}async refreshSettings(){try{const e=await this._msgBroker.sendToSidecar({type:st.getSoundSettings});e.data&&this._soundSettings.set(e.data)}catch(e){console.warn("Failed to refresh sound settings:",e)}}async unlockSound(){dt(this._soundSettings).enabled&&Un.unlockSoundForConfig(dt(this._soundSettings))}async playAgentComplete(){const e=dt(this._soundSettings);await Un.playSound(ti.AGENT_COMPLETE,e)}get getCurrentSettings(){return this._soundSettings}async initialize(){if(!this._isLoaded)try{const e=await this._msgBroker.sendToSidecar({type:st.getSoundSettings});e.data&&this._soundSettings.set(e.data),this._isLoaded=!0}catch(e){console.warn("Failed to load sound settings, using defaults:",e),this._soundSettings.set(Zn),this._isLoaded=!0}}async updateSettings(e){try{await this._msgBroker.sendToSidecar({type:st.updateSoundSettings,data:e}),this._soundSettings.update(t=>({...t,...e}))}catch(t){throw console.error("Failed to update sound settings:",t),t}}async setEnabled(e){await this.updateSettings({enabled:e})}async setVolume(e){const t=Math.max(0,Math.min(1,e));await this.updateSettings({volume:t})}async resetToDefaults(){await this.updateSettings(Zn)}updateEnabled(e){this.setEnabled(e).catch(t=>{console.error("Failed to update enabled setting:",t)})}updateVolume(e){this.setVolume(e).catch(t=>{console.error("Failed to update volume setting:",t)})}}W(Kn,"key","soundModel");var lt;let Ms=(lt=class{constructor(e){W(this,"_swarmModeSettings",ye(ct));W(this,"_isLoaded",!1);W(this,"_pollInterval",null);W(this,"_lastKnownSettingsHash","");W(this,"dispose",()=>{this.stopPolling()});this._msgBroker=e,this.initialize(),this.startPolling()}get getCurrentSettings(){return this._swarmModeSettings}async initialize(){if(!this._isLoaded)try{const e=await this._msgBroker.sendToSidecar({type:st.getSwarmModeSettings});e.data&&(this._swarmModeSettings.set(e.data),this._lastKnownSettingsHash=JSON.stringify(e.data)),this._isLoaded=!0}catch(e){console.warn("Failed to load swarm mode settings, using defaults:",e),this._swarmModeSettings.set(ct),this._lastKnownSettingsHash=JSON.stringify(ct),this._isLoaded=!0}}async updateSettings(e){try{const t=await this._msgBroker.sendToSidecar({type:st.updateSwarmModeSettings,data:e});t.data&&(this._swarmModeSettings.set(t.data),this._lastKnownSettingsHash=JSON.stringify(t.data))}catch(t){throw console.error("Failed to update swarm mode settings:",t),t}}async setEnabled(e){await this.updateSettings({enabled:e})}async resetToDefaults(){await this.updateSettings(ct)}updateEnabled(e){this.setEnabled(e).catch(t=>{console.error("Failed to update enabled setting:",t)})}startPolling(){this._pollInterval=setInterval(()=>{this.checkForUpdates()},lt.POLLING_INTERVAL_MS)}stopPolling(){this._pollInterval!==null&&(clearInterval(this._pollInterval),this._pollInterval=null)}async checkForUpdates(){try{const e=await this._msgBroker.sendToSidecar({type:st.getSwarmModeSettings}),t=JSON.stringify(e.data);this._lastKnownSettingsHash&&t!==this._lastKnownSettingsHash&&e.data&&this._swarmModeSettings.set(e.data),this._lastKnownSettingsHash=t}catch(e){console.warn("Failed to check for swarm mode settings updates:",e)}}},W(lt,"key","swarmModeModel"),W(lt,"POLLING_INTERVAL_MS",5e3),lt);function on(r,e){return t=>!t.shiftKey&&t.key===r&&(e(t),!0)}var nt=(r=>(r.file="file",r.folder="folder",r))(nt||{});class ut{constructor(e,t){W(this,"subscribe");W(this,"set");W(this,"update");W(this,"handleMessageFromExtension",async e=>{const t=e.data;switch(t.type){case ue.wsContextSourceFoldersChanged:case ue.wsContextFolderContentsChanged:this.updateSourceFolders(await this.getSourceFolders());break;case ue.sourceFoldersSyncStatus:this.update(n=>({...n,syncStatus:t.data.status}))}});W(this,"getSourceFolders",async()=>(await this.asyncMsgSender.send({type:ue.wsContextGetSourceFoldersRequest},1e4)).data.workspaceFolders);W(this,"getChildren",async e=>(await this.asyncMsgSender.send({type:ue.wsContextGetChildrenRequest,data:{fileId:e}},1e4)).data.children.map(t=>t.type==="folder"?{...t,children:[],expanded:!1}:{...t}).sort((t,n)=>t.type===n.type?t.name.localeCompare(n.name):t.type==="folder"?-1:1));this.host=e,this.asyncMsgSender=t;const{subscribe:n,set:s,update:o}=ye({sourceFolders:[],sourceTree:[],syncStatus:Hn.done});this.subscribe=n,this.set=s,this.update=o,this.getSourceFolders().then(i=>{this.update(l=>({...l,sourceFolders:i,sourceTree:ut.sourceFoldersToSourceNodes(i)}))})}async expandNode(e){e.children=await this.getChildren(e.fileId),e.expanded=!0,this.update(t=>t)}collapseNode(e){this.update(t=>(e.children=[],e.expanded=!1,t))}toggleNode(e){e.type==="folder"&&e.inclusionState!==qe.excluded&&(e.expanded?this.collapseNode(e):this.expandNode(e))}addMoreSourceFolders(){this.host.postMessage({type:ue.wsContextAddMoreSourceFolders})}removeSourceFolder(e){this.host.postMessage({type:ue.wsContextRemoveSourceFolder,data:e})}requestRefresh(){this.host.postMessage({type:ue.wsContextUserRequestedRefresh})}async updateSourceFolders(e){let t=dt(this);const n=await this.getRefreshedSourceTree(t.sourceTree,e);this.update(s=>({...s,sourceFolders:e,sourceTree:n}))}async getRefreshedSourceTree(e,t){const n=ut.sourceFoldersToSourceNodes(t);return this.getRefreshedSourceTreeRecurse(e,n)}async getRefreshedSourceTreeRecurse(e,t){const n=new Map(e.map(s=>[JSON.stringify([s.fileId.folderRoot,s.fileId.relPath]),s]));for(let s of t){const o=ut.fileIdToString(s.fileId);if(s.type==="folder"){const i=n.get(o);i&&(s.expanded=i.type==="folder"&&i.expanded,s.expanded&&(s.children=await this.getChildren(s.fileId),s.children=await this.getRefreshedSourceTreeRecurse(i.children,s.children)))}}return t}static fileIdToString(e){return JSON.stringify([e.folderRoot,e.relPath])}static sourceFoldersToSourceNodes(e){return e.filter(t=>!t.isNestedFolder&&!t.isPending).sort((t,n)=>t.name.localeCompare(n.name)).map(t=>({name:t.name,fileId:t.fileId,children:[],expanded:!1,type:"folder",inclusionState:t.inclusionState,reason:"",trackedFileCount:t.trackedFileCount}))}}function As(r,e,t){const n=r.slice();return n[6]=e[t],n}function Ns(r){let e,t;function n(){return r[5](r[6])}return e=new Zt({props:{title:"Remove source folder from Augment context",variant:"ghost",color:"neutral",size:1,class:"source-folder-v-adjust",$$slots:{default:[Wa]},$$scope:{ctx:r}}}),e.$on("click",function(){return r[4](r[6])}),e.$on("keyup",function(){_t(on("Enter",n))&&on("Enter",n).apply(this,arguments)}),{c(){S(e.$$.fragment)},m(s,o){x(e,s,o),t=!0},p(s,o){r=s;const i={};512&o&&(i.$$scope={dirty:o,ctx:r}),e.$set(i)},i(s){t||(m(e.$$.fragment,s),t=!0)},o(s){g(e.$$.fragment,s),t=!1},d(s){C(e,s)}}}function Wa(r){let e,t;return e=new ni({}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Ts(r){let e,t;return e=new Q({props:{size:1,class:"file-count",$$slots:{default:[Ka]},$$scope:{ctx:r}}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,s){const o={};513&s&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Ka(r){let e,t=r[6].trackedFileCount.toLocaleString()+"";return{c(){e=R(t)},m(n,s){y(n,e,s)},p(n,s){1&s&&t!==(t=n[6].trackedFileCount.toLocaleString()+"")&&de(e,t)},d(n){n&&v(e)}}}function Es(r,e){let t,n,s,o,i,l,a,c,d,u,f,p=e[6].name+"",h=(e[6].isPending?"(pending)":e[6].fileId.folderRoot)+"",$=!e[6].isWorkspaceFolder&&Ns(e);s=new bo({props:{class:"source-folder-v-adjust",icon:e[3](e[6])}});let b=e[6].trackedFileCount&&Ts(e);return{key:r,first:null,c(){t=k("div"),$&&$.c(),n=N(),S(s.$$.fragment),o=N(),i=k("span"),l=R(p),a=N(),c=k("span"),d=R(h),u=N(),b&&b.c(),w(c,"class","folderRoot svelte-1skknri"),w(i,"class","name svelte-1skknri"),w(t,"class","item svelte-1skknri"),he(t,"workspace-folder",e[6].isWorkspaceFolder),this.first=t},m(E,I){y(E,t,I),$&&$.m(t,null),_(t,n),x(s,t,null),_(t,o),_(t,i),_(i,l),_(i,a),_(i,c),_(c,d),_(t,u),b&&b.m(t,null),f=!0},p(E,I){(e=E)[6].isWorkspaceFolder?$&&(j(),g($,1,1,()=>{$=null}),V()):$?($.p(e,I),1&I&&m($,1)):($=Ns(e),$.c(),m($,1),$.m(t,n));const A={};1&I&&(A.icon=e[3](e[6])),s.$set(A),(!f||1&I)&&p!==(p=e[6].name+"")&&de(l,p),(!f||1&I)&&h!==(h=(e[6].isPending?"(pending)":e[6].fileId.folderRoot)+"")&&de(d,h),e[6].trackedFileCount?b?(b.p(e,I),1&I&&m(b,1)):(b=Ts(e),b.c(),m(b,1),b.m(t,null)):b&&(j(),g(b,1,1,()=>{b=null}),V()),(!f||1&I)&&he(t,"workspace-folder",e[6].isWorkspaceFolder)},i(E){f||(m($),m(s.$$.fragment,E),m(b),f=!0)},o(E){g($),g(s.$$.fragment,E),g(b),f=!1},d(E){E&&v(t),$&&$.d(),C(s),b&&b.d()}}}function Ya(r){let e,t,n,s,o,i,l,a,c=[],d=new Map,u=ce(r[0]);const f=p=>ut.fileIdToString(p[6].fileId);for(let p=0;p<u.length;p+=1){let h=As(r,u,p),$=f(h);d.set($,c[p]=Es($,h))}return s=new ln({}),{c(){e=k("div");for(let p=0;p<c.length;p+=1)c[p].c();t=N(),n=k("div"),S(s.$$.fragment),o=R(" Add more..."),w(n,"role","button"),w(n,"tabindex","0"),w(n,"class","add-more svelte-1skknri"),w(e,"class","source-folder svelte-1skknri")},m(p,h){y(p,e,h);for(let $=0;$<c.length;$+=1)c[$]&&c[$].m(e,null);_(e,t),_(e,n),x(s,n,null),_(n,o),i=!0,l||(a=[Ie(n,"keyup",function(){_t(on("Enter",r[1]))&&on("Enter",r[1]).apply(this,arguments)}),Ie(n,"click",function(){_t(r[1])&&r[1].apply(this,arguments)})],l=!0)},p(p,[h]){r=p,13&h&&(u=ce(r[0]),j(),c=Ke(c,h,f,1,r,u,d,e,Ye,Es,t,As),V())},i(p){if(!i){for(let h=0;h<u.length;h+=1)m(c[h]);m(s.$$.fragment,p),i=!0}},o(p){for(let h=0;h<c.length;h+=1)g(c[h]);g(s.$$.fragment,p),i=!1},d(p){p&&v(e);for(let h=0;h<c.length;h+=1)c[h].d();C(s),l=!1,kn(a)}}}function Xa(r,e,t){let{folders:n=[]}=e,{onAddMore:s}=e,{onRemove:o}=e;return r.$$set=i=>{"folders"in i&&t(0,n=i.folders),"onAddMore"in i&&t(1,s=i.onAddMore),"onRemove"in i&&t(2,o=i.onRemove)},[n,s,o,i=>i.isWorkspaceFolder?"root-folder":"folder",i=>o(i.fileId.folderRoot),i=>o(i.fileId.folderRoot)]}class Qa extends ae{constructor(e){super(),le(this,e,Xa,Ya,oe,{folders:0,onAddMore:1,onRemove:2})}}function Is(r,e,t){const n=r.slice();return n[10]=e[t],n}function Rs(r){let e,t;return e=new Q({props:{size:1,class:"file-count",$$slots:{default:[el]},$$scope:{ctx:r}}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,s){const o={};8193&s&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function el(r){let e,t=r[0].trackedFileCount.toLocaleString()+"";return{c(){e=R(t)},m(n,s){y(n,e,s)},p(n,s){1&s&&t!==(t=n[0].trackedFileCount.toLocaleString()+"")&&de(e,t)},d(n){n&&v(e)}}}function Ps(r){let e,t,n=[],s=new Map,o=ce(r[5].children);const i=l=>ut.fileIdToString(l[10].fileId);for(let l=0;l<o.length;l+=1){let a=Is(r,o,l),c=i(a);s.set(c,n[l]=Ls(c,a))}return{c(){e=k("div");for(let l=0;l<n.length;l+=1)n[l].c();w(e,"class","children-container")},m(l,a){y(l,e,a);for(let c=0;c<n.length;c+=1)n[c]&&n[c].m(e,null);t=!0},p(l,a){38&a&&(o=ce(l[5].children),j(),n=Ke(n,a,i,1,l,o,s,e,Ye,Ls,null,Is),V())},i(l){if(!t){for(let a=0;a<o.length;a+=1)m(n[a]);t=!0}},o(l){for(let a=0;a<n.length;a+=1)g(n[a]);t=!1},d(l){l&&v(e);for(let a=0;a<n.length;a+=1)n[a].d()}}}function Ls(r,e){let t,n,s;return n=new Uo({props:{data:e[10],wsContextModel:e[1],indentLevel:e[2]+1}}),{key:r,first:null,c(){t=ve(),S(n.$$.fragment),this.first=t},m(o,i){y(o,t,i),x(n,o,i),s=!0},p(o,i){e=o;const l={};32&i&&(l.data=e[10]),2&i&&(l.wsContextModel=e[1]),4&i&&(l.indentLevel=e[2]+1),n.$set(l)},i(o){s||(m(n.$$.fragment,o),s=!0)},o(o){g(n.$$.fragment,o),s=!1},d(o){o&&v(t),C(n,o)}}}function tl(r){let e,t,n,s,o,i,l,a,c,d,u,f,p,h,$,b,E,I,A=r[0].name+"";n=new bo({props:{icon:r[4]}});let M=r[0].type===nt.folder&&r[0].inclusionState!==qe.excluded&&typeof r[0].trackedFileCount=="number"&&Rs(r),T=r[5]&&Ps(r);return{c(){e=k("div"),t=k("div"),S(n.$$.fragment),s=N(),o=k("span"),i=R(A),l=N(),M&&M.c(),a=N(),c=k("img"),$=N(),T&&T.c(),w(o,"class","name svelte-sympus"),fs(c.src,d=r[7][r[0].inclusionState])||w(c,"src",d),w(c,"alt",u=r[8][r[0].inclusionState]),w(t,"class","tree-item svelte-sympus"),w(t,"role","treeitem"),w(t,"aria-selected","false"),w(t,"tabindex","0"),w(t,"title",f=r[0].reason),w(t,"aria-expanded",p=r[0].type===nt.folder&&r[0].expanded),w(t,"aria-level",r[2]),w(t,"style",h=`padding-left: ${10*r[2]+20}px;`),he(t,"included-folder",r[3])},m(O,G){y(O,e,G),_(e,t),x(n,t,null),_(t,s),_(t,o),_(o,i),_(t,l),M&&M.m(t,null),_(t,a),_(t,c),_(e,$),T&&T.m(e,null),b=!0,E||(I=[Ie(t,"click",r[6]),Ie(t,"keyup",on("Enter",r[6]))],E=!0)},p(O,[G]){const ne={};16&G&&(ne.icon=O[4]),n.$set(ne),(!b||1&G)&&A!==(A=O[0].name+"")&&de(i,A),O[0].type===nt.folder&&O[0].inclusionState!==qe.excluded&&typeof O[0].trackedFileCount=="number"?M?(M.p(O,G),1&G&&m(M,1)):(M=Rs(O),M.c(),m(M,1),M.m(t,a)):M&&(j(),g(M,1,1,()=>{M=null}),V()),(!b||1&G&&!fs(c.src,d=O[7][O[0].inclusionState]))&&w(c,"src",d),(!b||1&G&&u!==(u=O[8][O[0].inclusionState]))&&w(c,"alt",u),(!b||1&G&&f!==(f=O[0].reason))&&w(t,"title",f),(!b||1&G&&p!==(p=O[0].type===nt.folder&&O[0].expanded))&&w(t,"aria-expanded",p),(!b||4&G)&&w(t,"aria-level",O[2]),(!b||4&G&&h!==(h=`padding-left: ${10*O[2]+20}px;`))&&w(t,"style",h),(!b||8&G)&&he(t,"included-folder",O[3]),O[5]?T?(T.p(O,G),32&G&&m(T,1)):(T=Ps(O),T.c(),m(T,1),T.m(e,null)):T&&(j(),g(T,1,1,()=>{T=null}),V())},i(O){b||(m(n.$$.fragment,O),m(M),m(T),b=!0)},o(O){g(n.$$.fragment,O),g(M),g(T),b=!1},d(O){O&&v(e),C(n),M&&M.d(),T&&T.d(),E=!1,kn(I)}}}function nl(r,e,t){let{data:n}=e,{wsContextModel:s}=e,{indentLevel:o}=e;const i={[qe.included]:"data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01118C3.65328%200.351894%204.81331%200%206%200C7.5907%200.00195418%209.11569%200.634723%2010.2405%201.75952C11.3653%202.88431%2011.998%204.4093%2012%206C12%207.18669%2011.6481%208.34672%2010.9888%209.33342C10.3295%2010.3201%209.39246%2011.0891%208.2961%2011.5433C7.19975%2011.9974%205.99335%2012.1162%204.82946%2011.8847C3.66557%2011.6532%202.59648%2011.0818%201.75736%2010.2426C0.918247%209.40352%200.346802%208.33443%200.115291%207.17054C-0.11622%206.00666%200.00259969%204.80025%200.456725%203.7039C0.910851%202.60754%201.67989%201.67047%202.66658%201.01118ZM5.86301%208.67273L9.44256%203.9L8.24256%203L5.12729%207.15368L3.17471%205.59162L2.23767%206.76292L4.79449%208.80838L5.86301%208.67273Z'%20fill='%23388A34'/%3e%3c/svg%3e",[qe.excluded]:"data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01119C3.65328%200.351896%204.81332%200%206%200C7.5907%200.00195419%209.11569%200.634726%2010.2405%201.75953C11.3653%202.88433%2011.998%204.40933%2012%206.00003C12%207.18673%2011.6481%208.34677%2010.9888%209.33347C10.3295%2010.3202%209.39246%2011.0892%208.2961%2011.5433C7.19975%2011.9975%205.99335%2012.1163%204.82946%2011.8848C3.66558%2011.6533%202.59648%2011.0818%201.75736%2010.2427C0.918247%209.40358%200.346802%208.33447%200.115291%207.17058C-0.11622%206.00669%200.00259969%204.80028%200.456726%203.70392C0.910851%202.60756%201.67989%201.67048%202.66658%201.01119ZM6.00007%207.07359L8.1213%209.19482L9.18196%208.13416L7.06073%206.01292L9.18198%203.89166L8.12132%202.83099L6.00007%204.95225L3.87866%202.83083L2.818%203.89149L4.93941%206.01292L2.81802%208.13432L3.87868%209.19499L6.00007%207.07359Z'%20fill='%23E51400'/%3e%3c/svg%3e",[qe.partial]:"data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01118C3.65328%200.351894%204.81331%200%206%200C7.5907%200.00195418%209.11569%200.634723%2010.2405%201.75952C11.3653%202.88431%2011.998%204.4093%2012%206C12%207.18669%2011.6481%208.34672%2010.9888%209.33342C10.3295%2010.3201%209.39246%2011.0891%208.2961%2011.5433C7.19975%2011.9974%205.99335%2012.1162%204.82946%2011.8847C3.66557%2011.6532%202.59648%2011.0818%201.75736%2010.2426C0.918247%209.40352%200.346802%208.33443%200.115291%207.17054C-0.11622%206.00666%200.00259969%204.80025%200.456725%203.7039C0.910851%202.60754%201.67989%201.67047%202.66658%201.01118ZM3.66667%205.83333C3.66667%205.99815%203.61779%206.15927%203.52623%206.29631C3.43466%206.43335%203.30451%206.54016%203.15224%206.60323C2.99997%206.66631%202.83241%206.68281%202.67076%206.65065C2.50911%206.6185%202.36062%206.53913%202.24408%206.42259C2.12753%206.30605%202.04817%206.15756%202.01601%205.99591C1.98386%205.83426%202.00036%205.6667%202.06343%205.51443C2.12651%205.36216%202.23332%205.23201%202.37036%205.14044C2.5074%205.04887%202.66852%205%202.83333%205C3.05435%205%203.26631%205.0878%203.42259%205.24408C3.57887%205.40036%203.66667%205.61232%203.66667%205.83333ZM6.83333%205.83333C6.83333%205.99815%206.78446%206.15927%206.69289%206.29631C6.60132%206.43335%206.47117%206.54016%206.3189%206.60323C6.16663%206.66631%205.99908%206.68281%205.83742%206.65065C5.67577%206.6185%205.52729%206.53913%205.41074%206.42259C5.2942%206.30605%205.21483%206.15756%205.18268%205.99591C5.15052%205.83426%205.16703%205.6667%205.2301%205.51443C5.29317%205.36216%205.39998%205.23201%205.53702%205.14044C5.67407%205.04887%205.83518%205%206%205C6.22101%205%206.43297%205.0878%206.58926%205.24408C6.74554%205.40036%206.83333%205.61232%206.83333%205.83333ZM9.85956%206.29631C9.95113%206.15927%2010%205.99815%2010%205.83333C10%205.61232%209.9122%205.40036%209.75592%205.24408C9.59964%205.0878%209.38768%205%209.16667%205C9.00185%205%208.84073%205.04887%208.70369%205.14044C8.56665%205.23201%208.45984%205.36216%208.39677%205.51443C8.33369%205.6667%208.31719%205.83426%208.34935%205.99591C8.3815%206.15756%208.46087%206.30605%208.57741%206.42259C8.69395%206.53913%208.84244%206.6185%209.00409%206.65065C9.16574%206.68281%209.3333%206.66631%209.48557%206.60323C9.63784%206.54016%209.76799%206.43335%209.85956%206.29631Z'%20fill='%23388A34'/%3e%3c/svg%3e"},l={[qe.included]:"included",[qe.excluded]:"excluded",[qe.partial]:"partially included"};let a,c,d;return r.$$set=u=>{"data"in u&&t(0,n=u.data),"wsContextModel"in u&&t(1,s=u.wsContextModel),"indentLevel"in u&&t(2,o=u.indentLevel)},r.$$.update=()=>{var u;1&r.$$.dirty&&t(4,c=(u=n).type===nt.folder&&u.inclusionState!==qe.excluded?u.expanded?"chevron-down":"chevron-right":u.type===nt.folder?"folder":"file"),1&r.$$.dirty&&t(3,a=n.type===nt.folder&&n.inclusionState!==qe.excluded),1&r.$$.dirty&&t(5,d=n.type===nt.folder&&n.expanded&&n.children&&n.children.length>0?n:null)},[n,s,o,a,c,d,()=>{s.toggleNode(n)},i,l]}class Uo extends ae{constructor(e){super(),le(this,e,nl,tl,oe,{data:0,wsContextModel:1,indentLevel:2})}}function Os(r,e,t){const n=r.slice();return n[3]=e[t],n}function zs(r,e){let t,n,s;return n=new Uo({props:{wsContextModel:e[0],data:e[3],indentLevel:0}}),{key:r,first:null,c(){t=ve(),S(n.$$.fragment),this.first=t},m(o,i){y(o,t,i),x(n,o,i),s=!0},p(o,i){e=o;const l={};1&i&&(l.wsContextModel=e[0]),2&i&&(l.data=e[3]),n.$set(l)},i(o){s||(m(n.$$.fragment,o),s=!0)},o(o){g(n.$$.fragment,o),s=!1},d(o){o&&v(t),C(n,o)}}}function sl(r){let e,t,n=[],s=new Map,o=ce(r[1]);const i=l=>ut.fileIdToString(l[3].fileId);for(let l=0;l<o.length;l+=1){let a=Os(r,o,l),c=i(a);s.set(c,n[l]=zs(c,a))}return{c(){e=k("div");for(let l=0;l<n.length;l+=1)n[l].c();w(e,"class","files-container svelte-8hfqhl")},m(l,a){y(l,e,a);for(let c=0;c<n.length;c+=1)n[c]&&n[c].m(e,null);t=!0},p(l,[a]){3&a&&(o=ce(l[1]),j(),n=Ke(n,a,i,1,l,o,s,e,Ye,zs,null,Os),V())},i(l){if(!t){for(let a=0;a<o.length;a+=1)m(n[a]);t=!0}},o(l){for(let a=0;a<n.length;a+=1)g(n[a]);t=!1},d(l){l&&v(e);for(let a=0;a<n.length;a+=1)n[a].d()}}}function rl(r,e,t){let n,s=D,o=()=>(s(),s=an(l,a=>t(2,n=a)),l);r.$$.on_destroy.push(()=>s());let i,{wsContextModel:l}=e;return o(),r.$$set=a=>{"wsContextModel"in a&&o(t(0,l=a.wsContextModel))},r.$$.update=()=>{4&r.$$.dirty&&t(1,i=n.sourceTree)},[l,i,n]}class ol extends ae{constructor(e){super(),le(this,e,rl,sl,oe,{wsContextModel:0})}}function il(r){let e,t,n;return{c(){e=_e("svg"),t=_e("rect"),n=_e("path"),w(t,"width","16"),w(t,"height","16"),w(t,"transform","matrix(-1 0 0 -1 16 16)"),w(t,"fill","currentColor"),w(t,"fill-opacity","0.01"),w(n,"fill-rule","evenodd"),w(n,"clip-rule","evenodd"),w(n,"d","M13.7075 11.7333C13.7075 12.8236 12.8236 13.7075 11.7333 13.7075C10.643 13.7075 9.75909 12.8236 9.75909 11.7333C9.75909 10.643 10.643 9.75909 11.7333 9.75909C12.8236 9.75909 13.7075 10.643 13.7075 11.7333ZM11.7333 14.6675C13.3538 14.6675 14.6675 13.3538 14.6675 11.7333C14.6675 10.1128 13.3538 8.79909 11.7333 8.79909C10.1128 8.79909 8.79909 10.1128 8.79909 11.7333C8.79909 13.3538 10.1128 14.6675 11.7333 14.6675ZM9.79161 4.26647L13.3333 2.30721V6.22571L9.79161 4.26647ZM13.1852 7.24088C13.6829 7.51617 14.2933 7.15625 14.2933 6.58752V1.9454C14.2933 1.37665 13.6829 1.01676 13.1852 1.29207L8.98946 3.61313C8.47582 3.89729 8.47582 4.63564 8.98946 4.9198L13.1852 7.24088ZM7.14663 6.39988C7.14663 6.81225 6.81233 7.14654 6.39996 7.14654H2.1333C1.72093 7.14654 1.38664 6.81225 1.38664 6.39988V2.13324C1.38664 1.72087 1.72093 1.38657 2.1333 1.38657H6.39996C6.81233 1.38657 7.14663 1.72087 7.14663 2.13324V6.39988ZM6.18663 6.18654V2.34657H2.34664V6.18654H6.18663ZM1.66056 13.6606C1.47314 13.848 1.47314 14.152 1.66056 14.3394C1.84797 14.5269 2.15186 14.5269 2.33938 14.3394L4.26664 12.4121L6.19388 14.3394C6.38133 14.5268 6.68525 14.5268 6.8727 14.3394C7.06015 14.1519 7.06015 13.848 6.8727 13.6606L4.94546 11.7333L6.8727 9.80608C7.06015 9.61863 7.06015 9.31471 6.8727 9.12726C6.68525 8.9398 6.38133 8.9398 6.19388 9.12726L4.26664 11.0545L2.33938 9.12722C2.15186 8.93978 1.84797 8.93978 1.66056 9.12722C1.47314 9.31468 1.47314 9.61861 1.66056 9.80605L3.58781 11.7333L1.66056 13.6606Z"),w(n,"fill","currentColor"),w(e,"width","15"),w(e,"height","15"),w(e,"viewBox","0 0 16 16"),w(e,"fill","none"),w(e,"xmlns","http://www.w3.org/2000/svg")},m(s,o){y(s,e,o),_(e,t),_(e,n)},p:D,i:D,o:D,d(s){s&&v(e)}}}class al extends ae{constructor(e){super(),le(this,e,null,il,oe,{})}}const ll=r=>({}),Fs=r=>({}),cl=r=>({}),Zs=r=>({});function dl(r){let e;const t=r[8]["header-left"],n=Oe(t,r,r[10],Zs);return{c(){n&&n.c()},m(s,o){n&&n.m(s,o),e=!0},p(s,o){n&&n.p&&(!e||1024&o)&&ze(n,t,s,s[10],e?Ze(t,s[10],o,cl):Fe(s[10]),Zs)},i(s){e||(m(n,s),e=!0)},o(s){g(n,s),e=!1},d(s){n&&n.d(s)}}}function ul(r){let e,t,n,s=r[0]&&Us(r),o=r[1]&&Ds(r);return{c(){s&&s.c(),e=N(),o&&o.c(),t=ve()},m(i,l){s&&s.m(i,l),y(i,e,l),o&&o.m(i,l),y(i,t,l),n=!0},p(i,l){i[0]?s?(s.p(i,l),1&l&&m(s,1)):(s=Us(i),s.c(),m(s,1),s.m(e.parentNode,e)):s&&(j(),g(s,1,1,()=>{s=null}),V()),i[1]?o?(o.p(i,l),2&l&&m(o,1)):(o=Ds(i),o.c(),m(o,1),o.m(t.parentNode,t)):o&&(j(),g(o,1,1,()=>{o=null}),V())},i(i){n||(m(s),m(o),n=!0)},o(i){g(s),g(o),n=!1},d(i){i&&(v(e),v(t)),s&&s.d(i),o&&o.d(i)}}}function Us(r){let e,t,n;var s=r[0];return s&&(t=mt(s,{})),{c(){e=k("div"),t&&S(t.$$.fragment),w(e,"class","icon-wrapper svelte-13uht7n")},m(o,i){y(o,e,i),t&&x(t,e,null),n=!0},p(o,i){if(1&i&&s!==(s=o[0])){if(t){j();const l=t;g(l.$$.fragment,1,0,()=>{C(l,1)}),V()}s?(t=mt(s,{}),S(t.$$.fragment),m(t.$$.fragment,1),x(t,e,null)):t=null}},i(o){n||(t&&m(t.$$.fragment,o),n=!0)},o(o){t&&g(t.$$.fragment,o),n=!1},d(o){o&&v(e),t&&C(t)}}}function Ds(r){let e,t;return e=new Q({props:{color:"neutral",size:1,weight:"light",class:"card-title",$$slots:{default:[pl]},$$scope:{ctx:r}}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,s){const o={};1026&s&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function pl(r){let e;return{c(){e=R(r[1])},m(t,n){y(t,e,n)},p(t,n){2&n&&de(e,t[1])},d(t){t&&v(e)}}}function js(r){let e,t;const n=r[8].default,s=Oe(n,r,r[10],null);return{c(){e=k("div"),s&&s.c(),w(e,"class","settings-card-body")},m(o,i){y(o,e,i),s&&s.m(e,null),t=!0},p(o,i){s&&s.p&&(!t||1024&i)&&ze(s,n,o,o[10],t?Ze(n,o[10],i,null):Fe(o[10]),null)},i(o){t||(m(s,o),t=!0)},o(o){g(s,o),t=!1},d(o){o&&v(e),s&&s.d(o)}}}function ml(r){let e,t,n,s,o,i,l,a,c,d,u;const f=[ul,dl],p=[];function h(M,T){return M[0]||M[1]?0:1}s=h(r),o=p[s]=f[s](r);const $=r[8]["header-right"],b=Oe($,r,r[10],Fs);let E=r[5].default&&js(r),I=[{role:"button"},{class:r[3]},r[4]],A={};for(let M=0;M<I.length;M+=1)A=Ae(A,I[M]);return{c(){e=k("div"),t=k("div"),n=k("div"),o.c(),i=N(),l=k("div"),b&&b.c(),a=N(),E&&E.c(),w(n,"class","settings-card-left svelte-13uht7n"),w(l,"class","settings-card-right svelte-13uht7n"),w(t,"class","settings-card-content svelte-13uht7n"),gn(e,A),he(e,"clickable",r[2]),he(e,"svelte-13uht7n",!0)},m(M,T){y(M,e,T),_(e,t),_(t,n),p[s].m(n,null),_(t,i),_(t,l),b&&b.m(l,null),_(e,a),E&&E.m(e,null),c=!0,d||(u=Ie(e,"click",r[9]),d=!0)},p(M,[T]){let O=s;s=h(M),s===O?p[s].p(M,T):(j(),g(p[O],1,1,()=>{p[O]=null}),V(),o=p[s],o?o.p(M,T):(o=p[s]=f[s](M),o.c()),m(o,1),o.m(n,null)),b&&b.p&&(!c||1024&T)&&ze(b,$,M,M[10],c?Ze($,M[10],T,ll):Fe(M[10]),Fs),M[5].default?E?(E.p(M,T),32&T&&m(E,1)):(E=js(M),E.c(),m(E,1),E.m(e,null)):E&&(j(),g(E,1,1,()=>{E=null}),V()),gn(e,A=pt(I,[{role:"button"},(!c||8&T)&&{class:M[3]},16&T&&M[4]])),he(e,"clickable",M[2]),he(e,"svelte-13uht7n",!0)},i(M){c||(m(o),m(b,M),m(E),c=!0)},o(M){g(o),g(b,M),g(E),c=!1},d(M){M&&v(e),p[s].d(),b&&b.d(M),E&&E.d(),d=!1,u()}}}function fl(r,e,t){let n,s,o;const i=["class","icon","title","isClickable"];let l=$n(e,i),{$$slots:a={},$$scope:c}=e;const d=lo(a);let{class:u=""}=e,{icon:f}=e,{title:p}=e,{isClickable:h=!1}=e;return r.$$set=$=>{e=Ae(Ae({},e),We($)),t(11,l=$n(e,i)),"class"in $&&t(6,u=$.class),"icon"in $&&t(0,f=$.icon),"title"in $&&t(1,p=$.title),"isClickable"in $&&t(2,h=$.isClickable),"$$scope"in $&&t(10,c=$.$$scope)},r.$$.update=()=>{t(7,{class:n,...s}=l,n,(t(4,s),t(11,l))),192&r.$$.dirty&&t(3,o=`settings-card ${u} ${n||""}`)},[f,p,h,o,s,d,u,n,a,function($){co.call(this,r,$)},c]}let Do=class extends ae{constructor(r){super(),le(this,r,fl,ml,oe,{class:6,icon:0,title:1,isClickable:2})}};function gl(r){let e;return{c(){e=R("SOURCE FOLDERS")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function $l(r){let e;return{c(){e=R("FILES")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function hl(r){let e,t=r[2].toLocaleString()+"";return{c(){e=R(t)},m(n,s){y(n,e,s)},p(n,s){4&s&&t!==(t=n[2].toLocaleString()+"")&&de(e,t)},d(n){n&&v(e)}}}function vl(r){let e,t,n,s,o,i,l,a,c,d,u,f,p,h;return n=new Q({props:{size:1,weight:"medium",class:"context-section-header",$$slots:{default:[gl]},$$scope:{ctx:r}}}),o=new Qa({props:{folders:r[0],onRemove:r[7],onAddMore:r[8]}}),c=new Q({props:{size:1,weight:"medium",class:"context-section-header",$$slots:{default:[$l]},$$scope:{ctx:r}}}),u=new Q({props:{size:1,class:"file-count",$$slots:{default:[hl]},$$scope:{ctx:r}}}),p=new ol({props:{wsContextModel:r[3]}}),{c(){e=k("div"),t=k("div"),S(n.$$.fragment),s=N(),S(o.$$.fragment),i=N(),l=k("div"),a=k("div"),S(c.$$.fragment),d=N(),S(u.$$.fragment),f=N(),S(p.$$.fragment),w(a,"class","files-header svelte-qsnirf"),w(e,"class","context-list svelte-qsnirf")},m($,b){y($,e,b),_(e,t),x(n,t,null),_(t,s),x(o,t,null),_(e,i),_(e,l),_(l,a),x(c,a,null),_(a,d),x(u,a,null),_(l,f),x(p,l,null),h=!0},p($,b){const E={};512&b&&(E.$$scope={dirty:b,ctx:$}),n.$set(E);const I={};1&b&&(I.folders=$[0]),o.$set(I);const A={};512&b&&(A.$$scope={dirty:b,ctx:$}),c.$set(A);const M={};516&b&&(M.$$scope={dirty:b,ctx:$}),u.$set(M)},i($){h||(m(n.$$.fragment,$),m(o.$$.fragment,$),m(c.$$.fragment,$),m(u.$$.fragment,$),m(p.$$.fragment,$),h=!0)},o($){g(n.$$.fragment,$),g(o.$$.fragment,$),g(c.$$.fragment,$),g(u.$$.fragment,$),g(p.$$.fragment,$),h=!1},d($){$&&v(e),C(n),C(o),C(c),C(u),C(p)}}}function Vs(r){let e,t;return e=new Zt({props:{title:"Refresh",variant:"ghost-block",color:"neutral",size:1,$$slots:{default:[yl]},$$scope:{ctx:r}}}),e.$on("click",r[5]),e.$on("keyup",xi("Enter",r[6])),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,s){const o={};512&s&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function yl(r){let e,t;return e=new ai({}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function wl(r){let e,t,n=r[1]===Hn.done&&Vs(r);return{c(){e=k("div"),n&&n.c(),w(e,"slot","header-right")},m(s,o){y(s,e,o),n&&n.m(e,null),t=!0},p(s,o){s[1]===Hn.done?n?(n.p(s,o),2&o&&m(n,1)):(n=Vs(s),n.c(),m(n,1),n.m(e,null)):n&&(j(),g(n,1,1,()=>{n=null}),V())},i(s){t||(m(n),t=!0)},o(s){g(n),t=!1},d(s){s&&v(e),n&&n.d()}}}function Sl(r){let e,t,n,s;return e=new Do({props:{icon:al,title:"Context",$$slots:{"header-right":[wl],default:[vl]},$$scope:{ctx:r}}}),e.$on("contextmenu",xl),{c(){S(e.$$.fragment)},m(o,i){x(e,o,i),t=!0,n||(s=Ie(window,"message",r[3].handleMessageFromExtension),n=!0)},p(o,[i]){const l={};519&i&&(l.$$scope={dirty:i,ctx:o}),e.$set(l)},i(o){t||(m(e.$$.fragment,o),t=!0)},o(o){g(e.$$.fragment,o),t=!1},d(o){C(e,o),n=!1,s()}}}const xl=r=>r.preventDefault();function Cl(r,e,t){let n,s,o,i,l=new ut(Ne,new Ci(Ne.postMessage));return Pe(r,l,a=>t(4,s=a)),r.$$.update=()=>{16&r.$$.dirty&&t(0,o=s.sourceFolders.sort((a,c)=>a.isWorkspaceFolder!==c.isWorkspaceFolder?a.isWorkspaceFolder?-1:1:a.fileId.folderRoot.localeCompare(c.fileId.folderRoot))),16&r.$$.dirty&&t(1,i=s.syncStatus),1&r.$$.dirty&&t(2,n=o.reduce((a,c)=>a+(c.trackedFileCount??0),0))},[o,i,n,l,s,()=>l.requestRefresh(),()=>l.requestRefresh(),a=>l.removeSourceFolder(a),()=>l.addMoreSourceFolders()]}class _l extends ae{constructor(e){super(),le(this,e,Cl,Sl,oe,{})}}function jo(r){return function(e){switch(typeof e){case"object":return e!=null;case"function":return!0;default:return!1}}(r)&&"name"in r}function qs(r){return jo(r)&&"component"in r}function bl(r){let e,t;return{c(){e=_e("svg"),t=_e("path"),w(t,"d","M5.5 1.75V3H10.5V1.75C10.5 1.625 10.375 1.5 10.25 1.5H5.75C5.59375 1.5 5.5 1.625 5.5 1.75ZM4 3V1.75C4 0.8125 4.78125 0 5.75 0H10.25C11.1875 0 12 0.8125 12 1.75V3H14C15.0938 3 16 3.90625 16 5V8.75V13C16 14.125 15.0938 15 14 15H2C0.875 15 0 14.125 0 13V8.75V5C0 3.90625 0.875 3 2 3H4ZM1.5 9.5V13C1.5 13.2812 1.71875 13.5 2 13.5H14C14.25 13.5 14.5 13.2812 14.5 13V9.5H10V10C10 10.5625 9.53125 11 9 11H7C6.4375 11 6 10.5625 6 10V9.5H1.5ZM6 8H10H14.5V5C14.5 4.75 14.25 4.5 14 4.5H11.25H4.75H2C1.71875 4.5 1.5 4.75 1.5 5V8H6Z"),w(t,"fill","currentColor"),w(e,"width","16"),w(e,"height","15"),w(e,"viewBox","0 0 16 15"),w(e,"xmlns","http://www.w3.org/2000/svg")},m(n,s){y(n,e,s),_(e,t)},p:D,i:D,o:D,d(n){n&&v(e)}}}class Vo extends ae{constructor(e){super(),le(this,e,null,bl,oe,{})}}const kl=r=>({item:1&r}),Hs=r=>({item:r[0]}),Ml=r=>({}),Bs=r=>({});function Gs(r){var c;let e,t,n,s,o;e=new Q({props:{size:4,weight:"medium",color:"neutral",$$slots:{default:[Al]},$$scope:{ctx:r}}});let i=((c=r[0])==null?void 0:c.description)&&Js(r);const l=r[1].content,a=Oe(l,r,r[2],Hs);return{c(){S(e.$$.fragment),t=N(),i&&i.c(),n=N(),s=k("div"),a&&a.c(),w(s,"class","c-navigation__content-container svelte-z0ijuz")},m(d,u){x(e,d,u),y(d,t,u),i&&i.m(d,u),y(d,n,u),y(d,s,u),a&&a.m(s,null),o=!0},p(d,u){var p;const f={};5&u&&(f.$$scope={dirty:u,ctx:d}),e.$set(f),(p=d[0])!=null&&p.description?i?(i.p(d,u),1&u&&m(i,1)):(i=Js(d),i.c(),m(i,1),i.m(n.parentNode,n)):i&&(j(),g(i,1,1,()=>{i=null}),V()),a&&a.p&&(!o||5&u)&&ze(a,l,d,d[2],o?Ze(l,d[2],u,kl):Fe(d[2]),Hs)},i(d){o||(m(e.$$.fragment,d),m(i),m(a,d),o=!0)},o(d){g(e.$$.fragment,d),g(i),g(a,d),o=!1},d(d){d&&(v(t),v(n),v(s)),C(e,d),i&&i.d(d),a&&a.d(d)}}}function Al(r){var s;let e,t,n=((s=r[0])==null?void 0:s.name)+"";return{c(){e=k("div"),t=R(n),w(e,"class","c-navigation__content-header svelte-z0ijuz")},m(o,i){y(o,e,i),_(e,t)},p(o,i){var l;1&i&&n!==(n=((l=o[0])==null?void 0:l.name)+"")&&de(t,n)},d(o){o&&v(e)}}}function Js(r){let e,t;return e=new Q({props:{color:"secondary",size:1,weight:"light",$$slots:{default:[Nl]},$$scope:{ctx:r}}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,s){const o={};5&s&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Nl(r){var s;let e,t,n=((s=r[0])==null?void 0:s.description)+"";return{c(){e=k("div"),t=R(n),w(e,"class","c-navigation__content-description svelte-z0ijuz")},m(o,i){y(o,e,i),_(e,t)},p(o,i){var l;1&i&&n!==(n=((l=o[0])==null?void 0:l.description)+"")&&de(t,n)},d(o){o&&v(e)}}}function Tl(r){let e,t,n,s,o;const i=r[1].header,l=Oe(i,r,r[2],Bs);let a=r[0]!=null&&Gs(r);return{c(){var c;e=k("div"),l&&l.c(),t=N(),n=k("div"),a&&a.c(),w(e,"class","c-navigation__content svelte-z0ijuz"),w(e,"id",s=(c=r[0])==null?void 0:c.id)},m(c,d){y(c,e,d),l&&l.m(e,null),_(e,t),_(e,n),a&&a.m(n,null),o=!0},p(c,[d]){var u;l&&l.p&&(!o||4&d)&&ze(l,i,c,c[2],o?Ze(i,c[2],d,Ml):Fe(c[2]),Bs),c[0]!=null?a?(a.p(c,d),1&d&&m(a,1)):(a=Gs(c),a.c(),m(a,1),a.m(n,null)):a&&(j(),g(a,1,1,()=>{a=null}),V()),(!o||1&d&&s!==(s=(u=c[0])==null?void 0:u.id))&&w(e,"id",s)},i(c){o||(m(l,c),m(a),o=!0)},o(c){g(l,c),g(a),o=!1},d(c){c&&v(e),l&&l.d(c),a&&a.d()}}}function El(r,e,t){let{$$slots:n={},$$scope:s}=e,{item:o}=e;return r.$$set=i=>{"item"in i&&t(0,o=i.item),"$$scope"in i&&t(2,s=i.$$scope)},[o,n,s]}class qo extends ae{constructor(e){super(),le(this,e,El,Tl,oe,{item:0})}}function Il(r,e){let t;function n({scrollTo:s,delay:o,options:i}){clearTimeout(t),s&&(t=setTimeout(()=>{r.scrollIntoView(i)},o))}return n(e),{update:n,destroy(){clearTimeout(t)}}}function Ws(r,e,t){const n=r.slice();return n[13]=e[t][0],n[14]=e[t][1],n}function Ks(r,e,t){const n=r.slice();return n[22]=e[t],n}const Rl=r=>({item:32&r}),Ys=r=>({slot:"content",item:r[22]}),Pl=r=>({label:32&r,mode:4&r}),Xs=r=>({label:r[13],mode:r[2]}),Ll=r=>({item:1&r}),Qs=r=>({item:r[0]});function er(r,e,t){const n=r.slice();return n[13]=e[t][0],n[14]=e[t][1],n}function tr(r,e,t){const n=r.slice();return n[17]=e[t],n}const Ol=r=>({label:32&r,mode:4&r}),nr=r=>({label:r[13],mode:r[2]}),zl=r=>({item:1&r,selectedId:2&r}),sr=r=>({slot:"header",item:r[0],selectedId:r[1]}),Fl=r=>({item:1&r,isSelected:3&r}),rr=r=>{var e;return{slot:"content",item:r[0],isSelected:((e=r[0])==null?void 0:e.id)===r[1]}};function Zl(r){let e,t,n;const s=r[10].header,o=Oe(s,r,r[12],Qs);let i=ce(r[5]),l=[];for(let c=0;c<i.length;c+=1)l[c]=ir(Ws(r,i,c));const a=c=>g(l[c],1,1,()=>{l[c]=null});return{c(){e=k("div"),o&&o.c(),t=N();for(let c=0;c<l.length;c+=1)l[c].c();w(e,"class","c-navigation__flat svelte-n5ccbo")},m(c,d){y(c,e,d),o&&o.m(e,null),_(e,t);for(let u=0;u<l.length;u+=1)l[u]&&l[u].m(e,null);n=!0},p(c,d){if(o&&o.p&&(!n||4097&d)&&ze(o,s,c,c[12],n?Ze(s,c[12],d,Ll):Fe(c[12]),Qs),4134&d){let u;for(i=ce(c[5]),u=0;u<i.length;u+=1){const f=Ws(c,i,u);l[u]?(l[u].p(f,d),m(l[u],1)):(l[u]=ir(f),l[u].c(),m(l[u],1),l[u].m(e,null))}for(j(),u=i.length;u<l.length;u+=1)a(u);V()}},i(c){if(!n){m(o,c);for(let d=0;d<i.length;d+=1)m(l[d]);n=!0}},o(c){g(o,c),l=l.filter(Boolean);for(let d=0;d<l.length;d+=1)g(l[d]);n=!1},d(c){c&&v(e),o&&o.d(c),yt(l,c)}}}function Ul(r){let e,t;return e=new _i({props:{initialWidth:200,expandedMinWidth:150,columnLayoutThreshold:0,showButton:r[3],minimized:!1,$$slots:{right:[Jl],left:[Hl]},$$scope:{ctx:r}}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,s){const o={};8&s&&(o.showButton=n[3]),4135&s&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Dl(r){let e,t,n,s,o,i,l=r[13]+"";return t=new Vo({}),{c(){e=k("span"),S(t.$$.fragment),n=N(),s=k("span"),o=R(l),w(e,"class","c-navigation__head-icon")},m(a,c){y(a,e,c),x(t,e,null),y(a,n,c),y(a,s,c),_(s,o),i=!0},p(a,c){(!i||32&c)&&l!==(l=a[13]+"")&&de(o,l)},i(a){i||(m(t.$$.fragment,a),i=!0)},o(a){g(t.$$.fragment,a),i=!1},d(a){a&&(v(e),v(n),v(s)),C(t)}}}function jl(r){let e;const t=r[10].content,n=Oe(t,r,r[12],Ys);return{c(){n&&n.c()},m(s,o){n&&n.m(s,o),e=!0},p(s,o){n&&n.p&&(!e||4128&o)&&ze(n,t,s,s[12],e?Ze(t,s[12],o,Rl):Fe(s[12]),Ys)},i(s){e||(m(n,s),e=!0)},o(s){g(n,s),e=!1},d(s){n&&n.d(s)}}}function or(r){let e,t,n,s,o,i,l;return t=new qo({props:{item:r[22],$$slots:{content:[jl]},$$scope:{ctx:r}}}),{c(){e=k("span"),S(t.$$.fragment),n=N()},m(a,c){y(a,e,c),x(t,e,null),_(e,n),o=!0,i||(l=Qo(s=Il.call(null,e,{scrollTo:r[2]==="flat"&&r[22].id===r[1],delay:300,options:{behavior:"smooth"}})),i=!0)},p(a,c){r=a;const d={};32&c&&(d.item=r[22]),4128&c&&(d.$$scope={dirty:c,ctx:r}),t.$set(d),s&&_t(s.update)&&38&c&&s.update.call(null,{scrollTo:r[2]==="flat"&&r[22].id===r[1],delay:300,options:{behavior:"smooth"}})},i(a){o||(m(t.$$.fragment,a),o=!0)},o(a){g(t.$$.fragment,a),o=!1},d(a){a&&v(e),C(t),i=!1,l()}}}function ir(r){let e,t,n,s;const o=r[10].group,i=Oe(o,r,r[12],Xs),l=i||function(u){let f,p;return f=new Q({props:{color:"secondary",size:2,weight:"medium",$$slots:{default:[Dl]},$$scope:{ctx:u}}}),{c(){S(f.$$.fragment)},m(h,$){x(f,h,$),p=!0},p(h,$){const b={};4128&$&&(b.$$scope={dirty:$,ctx:h}),f.$set(b)},i(h){p||(m(f.$$.fragment,h),p=!0)},o(h){g(f.$$.fragment,h),p=!1},d(h){C(f,h)}}}(r);let a=ce(r[14]),c=[];for(let u=0;u<a.length;u+=1)c[u]=or(Ks(r,a,u));const d=u=>g(c[u],1,1,()=>{c[u]=null});return{c(){e=k("div"),l&&l.c(),t=N();for(let u=0;u<c.length;u+=1)c[u].c();n=ve(),w(e,"class","c-navigation__head svelte-n5ccbo")},m(u,f){y(u,e,f),l&&l.m(e,null),y(u,t,f);for(let p=0;p<c.length;p+=1)c[p]&&c[p].m(u,f);y(u,n,f),s=!0},p(u,f){if(i?i.p&&(!s||4132&f)&&ze(i,o,u,u[12],s?Ze(o,u[12],f,Pl):Fe(u[12]),Xs):l&&l.p&&(!s||32&f)&&l.p(u,s?f:-1),4134&f){let p;for(a=ce(u[14]),p=0;p<a.length;p+=1){const h=Ks(u,a,p);c[p]?(c[p].p(h,f),m(c[p],1)):(c[p]=or(h),c[p].c(),m(c[p],1),c[p].m(n.parentNode,n))}for(j(),p=a.length;p<c.length;p+=1)d(p);V()}},i(u){if(!s){m(l,u);for(let f=0;f<a.length;f+=1)m(c[f]);s=!0}},o(u){g(l,u),c=c.filter(Boolean);for(let f=0;f<c.length;f+=1)g(c[f]);s=!1},d(u){u&&(v(e),v(t),v(n)),l&&l.d(u),yt(c,u)}}}function Vl(r){let e,t=r[13]+"";return{c(){e=R(t)},m(n,s){y(n,e,s)},p(n,s){32&s&&t!==(t=n[13]+"")&&de(e,t)},d(n){n&&v(e)}}}function ql(r){let e,t,n,s,o,i=r[17].name+"";var l=r[17].icon;return l&&(t=mt(l,{})),{c(){e=k("span"),t&&S(t.$$.fragment),n=N(),s=R(i),w(e,"class","c-navigation__head-icon")},m(a,c){y(a,e,c),t&&x(t,e,null),y(a,n,c),y(a,s,c),o=!0},p(a,c){if(32&c&&l!==(l=a[17].icon)){if(t){j();const d=t;g(d.$$.fragment,1,0,()=>{C(d,1)}),V()}l?(t=mt(l,{}),S(t.$$.fragment),m(t.$$.fragment,1),x(t,e,null)):t=null}(!o||32&c)&&i!==(i=a[17].name+"")&&de(s,i)},i(a){o||(t&&m(t.$$.fragment,a),o=!0)},o(a){t&&g(t.$$.fragment,a),o=!1},d(a){a&&(v(e),v(n),v(s)),t&&C(t)}}}function ar(r){let e,t,n,s,o,i;function l(){return r[11](r[17])}return t=new Q({props:{size:2,weight:"regular",color:"primary",$$slots:{default:[ql]},$$scope:{ctx:r}}}),{c(){e=k("button"),S(t.$$.fragment),n=N(),w(e,"class","c-navigation__item svelte-n5ccbo"),he(e,"is-active",r[17].id===r[1])},m(a,c){y(a,e,c),x(t,e,null),_(e,n),s=!0,o||(i=Ie(e,"click",l),o=!0)},p(a,c){r=a;const d={};4128&c&&(d.$$scope={dirty:c,ctx:r}),t.$set(d),(!s||34&c)&&he(e,"is-active",r[17].id===r[1])},i(a){s||(m(t.$$.fragment,a),s=!0)},o(a){g(t.$$.fragment,a),s=!1},d(a){a&&v(e),C(t),o=!1,i()}}}function lr(r){let e,t,n,s,o;const i=r[10].group,l=Oe(i,r,r[12],nr),a=l||function(f){let p,h,$,b,E;return h=new Vo({}),b=new Q({props:{size:2,color:"primary",$$slots:{default:[Vl]},$$scope:{ctx:f}}}),{c(){p=k("div"),S(h.$$.fragment),$=N(),S(b.$$.fragment),w(p,"class","c-navigation__head svelte-n5ccbo")},m(I,A){y(I,p,A),x(h,p,null),_(p,$),x(b,p,null),E=!0},p(I,A){const M={};4128&A&&(M.$$scope={dirty:A,ctx:I}),b.$set(M)},i(I){E||(m(h.$$.fragment,I),m(b.$$.fragment,I),E=!0)},o(I){g(h.$$.fragment,I),g(b.$$.fragment,I),E=!1},d(I){I&&v(p),C(h),C(b)}}}(r);let c=ce(r[14]),d=[];for(let f=0;f<c.length;f+=1)d[f]=ar(tr(r,c,f));const u=f=>g(d[f],1,1,()=>{d[f]=null});return{c(){e=k("div"),a&&a.c(),t=N(),n=k("div");for(let f=0;f<d.length;f+=1)d[f].c();s=N(),w(n,"class","c-navigation__items svelte-n5ccbo"),w(e,"class","c-navigation__group")},m(f,p){y(f,e,p),a&&a.m(e,null),_(e,t),_(e,n);for(let h=0;h<d.length;h+=1)d[h]&&d[h].m(n,null);_(e,s),o=!0},p(f,p){if(l?l.p&&(!o||4132&p)&&ze(l,i,f,f[12],o?Ze(i,f[12],p,Ol):Fe(f[12]),nr):a&&a.p&&(!o||32&p)&&a.p(f,o?p:-1),98&p){let h;for(c=ce(f[14]),h=0;h<c.length;h+=1){const $=tr(f,c,h);d[h]?(d[h].p($,p),m(d[h],1)):(d[h]=ar($),d[h].c(),m(d[h],1),d[h].m(n,null))}for(j(),h=c.length;h<d.length;h+=1)u(h);V()}},i(f){if(!o){m(a,f);for(let p=0;p<c.length;p+=1)m(d[p]);o=!0}},o(f){g(a,f),d=d.filter(Boolean);for(let p=0;p<d.length;p+=1)g(d[p]);o=!1},d(f){f&&v(e),a&&a.d(f),yt(d,f)}}}function cr(r){let e,t,n=ce(r[5]),s=[];for(let i=0;i<n.length;i+=1)s[i]=lr(er(r,n,i));const o=i=>g(s[i],1,1,()=>{s[i]=null});return{c(){for(let i=0;i<s.length;i+=1)s[i].c();e=ve()},m(i,l){for(let a=0;a<s.length;a+=1)s[a]&&s[a].m(i,l);y(i,e,l),t=!0},p(i,l){if(4198&l){let a;for(n=ce(i[5]),a=0;a<n.length;a+=1){const c=er(i,n,a);s[a]?(s[a].p(c,l),m(s[a],1)):(s[a]=lr(c),s[a].c(),m(s[a],1),s[a].m(e.parentNode,e))}for(j(),a=n.length;a<s.length;a+=1)o(a);V()}},i(i){if(!t){for(let l=0;l<n.length;l+=1)m(s[l]);t=!0}},o(i){s=s.filter(Boolean);for(let l=0;l<s.length;l+=1)g(s[l]);t=!1},d(i){i&&v(e),yt(s,i)}}}function Hl(r){let e,t,n=r[1],s=cr(r);return{c(){e=k("nav"),s.c(),w(e,"class","c-navigation__nav svelte-n5ccbo"),w(e,"slot","left")},m(o,i){y(o,e,i),s.m(e,null),t=!0},p(o,i){2&i&&oe(n,n=o[1])?(j(),g(s,1,1,D),V(),s=cr(o),s.c(),m(s,1),s.m(e,null)):s.p(o,i)},i(o){t||(m(s),t=!0)},o(o){g(s),t=!1},d(o){o&&v(e),s.d(o)}}}function Bl(r){let e;const t=r[10].header,n=Oe(t,r,r[12],sr);return{c(){n&&n.c()},m(s,o){n&&n.m(s,o),e=!0},p(s,o){n&&n.p&&(!e||4099&o)&&ze(n,t,s,s[12],e?Ze(t,s[12],o,zl):Fe(s[12]),sr)},i(s){e||(m(n,s),e=!0)},o(s){g(n,s),e=!1},d(s){n&&n.d(s)}}}function dr(r){let e,t,n;const s=[r[0].props];var o=r[0].component;function i(l,a){let c={};for(let d=0;d<s.length;d+=1)c=Ae(c,s[d]);return a!==void 0&&1&a&&(c=Ae(c,pt(s,[gs(l[0].props)]))),{props:c}}return o&&(e=mt(o,i(r))),{c(){e&&S(e.$$.fragment),t=ve()},m(l,a){e&&x(e,l,a),y(l,t,a),n=!0},p(l,a){if(1&a&&o!==(o=l[0].component)){if(e){j();const c=e;g(c.$$.fragment,1,0,()=>{C(c,1)}),V()}o?(e=mt(o,i(l,a)),S(e.$$.fragment),m(e.$$.fragment,1),x(e,t.parentNode,t)):e=null}else if(o){const c=1&a?pt(s,[gs(l[0].props)]):{};e.$set(c)}},i(l){n||(e&&m(e.$$.fragment,l),n=!0)},o(l){e&&g(e.$$.fragment,l),n=!1},d(l){l&&v(t),e&&C(e,l)}}}function Gl(r){let e;const t=r[10].content,n=Oe(t,r,r[12],rr),s=n||function(o){let i,l,a=qs(o[0])&&ur(o[0],o[2],o[1]),c=a&&dr(o);return{c(){c&&c.c(),i=ve()},m(d,u){c&&c.m(d,u),y(d,i,u),l=!0},p(d,u){7&u&&(a=qs(d[0])&&ur(d[0],d[2],d[1])),a?c?(c.p(d,u),7&u&&m(c,1)):(c=dr(d),c.c(),m(c,1),c.m(i.parentNode,i)):c&&(j(),g(c,1,1,()=>{c=null}),V())},i(d){l||(m(c),l=!0)},o(d){g(c),l=!1},d(d){d&&v(i),c&&c.d(d)}}}(r);return{c(){s&&s.c()},m(o,i){s&&s.m(o,i),e=!0},p(o,i){n?n.p&&(!e||4099&i)&&ze(n,t,o,o[12],e?Ze(t,o[12],i,Fl):Fe(o[12]),rr):s&&s.p&&(!e||7&i)&&s.p(o,e?i:-1)},i(o){e||(m(s,o),e=!0)},o(o){g(s,o),e=!1},d(o){s&&s.d(o)}}}function Jl(r){let e,t;return e=new qo({props:{item:r[0],slot:"right",$$slots:{content:[Gl],header:[Bl]},$$scope:{ctx:r}}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,s){const o={};1&s&&(o.item=n[0]),4103&s&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Wl(r){let e,t,n,s,o;const i=[Ul,Zl],l=[];function a(c,d){return c[2]==="tree"?0:1}return t=a(r),n=l[t]=i[t](r),{c(){e=k("div"),n.c(),w(e,"class",s="c-navigation c-navigation--mode__"+r[2]+" "+r[4]+" svelte-n5ccbo")},m(c,d){y(c,e,d),l[t].m(e,null),o=!0},p(c,[d]){let u=t;t=a(c),t===u?l[t].p(c,d):(j(),g(l[u],1,1,()=>{l[u]=null}),V(),n=l[t],n?n.p(c,d):(n=l[t]=i[t](c),n.c()),m(n,1),n.m(e,null)),(!o||20&d&&s!==(s="c-navigation c-navigation--mode__"+c[2]+" "+c[4]+" svelte-n5ccbo"))&&w(e,"class",s)},i(c){o||(m(n),o=!0)},o(c){g(n),o=!1},d(c){c&&v(e),l[t].d()}}}function mn(r,e,t,n,s,o){return{name:r,description:e,icon:t,id:n}}function ur(r,e,t){return e!=="tree"||(r==null?void 0:r.id)===t}function Kl(r,e,t){let{$$slots:n={},$$scope:s}=e,{group:o="Workspace Settings"}=e,{items:i=[]}=e,{item:l}=e,{mode:a="tree"}=e,{selectedId:c}=e,{onNavigationChangeItem:d=$=>{}}=e,{showButton:u=!0}=e,{class:f=""}=e,p=new Map;function h($){t(0,l=$),t(1,c=$==null?void 0:$.id)}return r.$$set=$=>{"group"in $&&t(7,o=$.group),"items"in $&&t(8,i=$.items),"item"in $&&t(0,l=$.item),"mode"in $&&t(2,a=$.mode),"selectedId"in $&&t(1,c=$.selectedId),"onNavigationChangeItem"in $&&t(9,d=$.onNavigationChangeItem),"showButton"in $&&t(3,u=$.showButton),"class"in $&&t(4,f=$.class),"$$scope"in $&&t(12,s=$.$$scope)},r.$$.update=()=>{259&r.$$.dirty&&(c?t(0,l=i.find($=>($==null?void 0:$.id)===c)):t(1,c=l==null?void 0:l.id)),384&r.$$.dirty&&t(5,p=i.reduce(($,b)=>{if(!b)return $;const E=b.group??o,I=$.get(E)??[];return I.push(b),$.set(E,I),$},new Map)),257&r.$$.dirty&&(l||t(0,l=i[0])),514&r.$$.dirty&&d(c)},[l,c,a,u,f,p,h,o,i,d,n,$=>h($),s]}class Yl extends ae{constructor(e){super(),le(this,e,Kl,Wl,oe,{group:7,items:8,item:0,mode:2,selectedId:1,onNavigationChangeItem:9,showButton:3,class:4})}}function Xl(r){let e,t;return{c(){e=_e("svg"),t=_e("path"),w(t,"d","M3.13281 0.886719L5.97656 3.07422C6.14062 3.21094 6.25 3.40234 6.25 3.59375V5.07031L9.23047 8.05078C10.0234 7.66797 11.0078 7.80469 11.6641 8.46094L14.7266 11.5234C15.082 11.8516 15.082 12.4258 14.7266 12.7539L12.9766 14.5039C12.6484 14.8594 12.0742 14.8594 11.7461 14.5039L8.68359 11.4414C8.02734 10.7852 7.89062 9.77344 8.30078 8.98047L5.32031 6H3.81641C3.625 6 3.43359 5.91797 3.29688 5.75391L1.10938 2.91016C0.917969 2.63672 0.945312 2.28125 1.19141 2.03516L2.28516 0.941406C2.50391 0.722656 2.88672 0.695312 3.13281 0.886719ZM1.62891 11.0586L5.375 7.3125L6.30469 8.24219L2.55859 11.9883C2.39453 12.1523 2.3125 12.3711 2.3125 12.5898C2.3125 13.0547 2.69531 13.4375 3.16016 13.4375C3.37891 13.4375 3.59766 13.3555 3.76172 13.1914L7.17969 9.77344C7.15234 10.293 7.26172 10.8125 7.50781 11.3047L4.69141 14.1211C4.28125 14.5312 3.73438 14.75 3.16016 14.75C1.95703 14.75 1 13.793 1 12.5898C1 12.0156 1.21875 11.4688 1.62891 11.0586ZM13.6602 5.23438L12.9766 5.94531C12.6484 6.27344 12.2109 6.46484 11.7461 6.46484H11.0625C10.0781 6.46484 9.3125 5.67188 9.3125 4.71484V4.00391C9.3125 3.53906 9.47656 3.10156 9.80469 2.77344L10.5156 2.08984C8.875 2.14453 7.5625 3.48438 7.5625 5.125V5.15234L7.125 4.71484V3.59375C7.125 3.32031 7.04297 3.04688 6.90625 2.82812C7.67188 1.59766 9.03906 0.75 10.625 0.75C11.2812 0.75 11.9375 0.914062 12.5117 1.1875C12.7578 1.32422 12.7852 1.65234 12.5938 1.84375L10.7344 3.70312C10.6523 3.78516 10.625 3.89453 10.625 4.00391V4.6875C10.625 4.93359 10.8164 5.125 11.0625 5.125L11.7461 5.15234C11.8555 5.15234 11.9648 5.09766 12.0469 5.01562L13.9062 3.15625C14.0977 2.96484 14.4258 2.99219 14.5625 3.23828C14.8359 3.8125 15 4.46875 15 5.15234C15 6.60156 14.2617 7.91406 13.1406 8.70703L12.293 7.83203C12.2656 7.80469 12.2383 7.77734 12.2109 7.75C13.0586 7.23047 13.6328 6.30078 13.6602 5.23438Z"),w(t,"fill","currentColor"),w(e,"width","16"),w(e,"height","16"),w(e,"viewBox","0 0 16 16"),w(e,"xmlns","http://www.w3.org/2000/svg")},m(n,s){y(n,e,s),_(e,t)},p:D,i:D,o:D,d(n){n&&v(e)}}}class Ql extends ae{constructor(e){super(),le(this,e,null,Xl,oe,{})}}function ec(r){let e,t;return{c(){e=_e("svg"),t=_e("path"),w(t,"d","M6.25 10.8125H12.375C12.5938 10.8125 12.8125 10.6211 12.8125 10.375V4.25H11.5C11.0078 4.25 10.625 3.86719 10.625 3.375V2.0625H6.25C6.00391 2.0625 5.8125 2.28125 5.8125 2.5V10.375C5.8125 10.6211 6.00391 10.8125 6.25 10.8125ZM12.375 12.125H6.25C5.26562 12.125 4.5 11.3594 4.5 10.375V2.5C4.5 1.54297 5.26562 0.75 6.25 0.75H10.7617C11.2266 0.75 11.6641 0.941406 11.9922 1.26953L13.6055 2.88281C13.9336 3.21094 14.125 3.64844 14.125 4.11328V10.375C14.125 11.3594 13.332 12.125 12.375 12.125ZM2.53125 3.375C2.88672 3.375 3.1875 3.67578 3.1875 4.03125V11.0312C3.1875 12.3711 4.25391 13.4375 5.59375 13.4375H10.8438C11.1992 13.4375 11.5 13.7383 11.5 14.0938C11.5 14.4766 11.1992 14.75 10.8438 14.75H5.59375C3.51562 14.75 1.875 13.1094 1.875 11.0312V4.03125C1.875 3.67578 2.14844 3.375 2.53125 3.375Z"),w(t,"fill","currentColor"),w(e,"width","16"),w(e,"height","16"),w(e,"viewBox","0 0 16 16"),w(e,"xmlns","http://www.w3.org/2000/svg")},m(n,s){y(n,e,s),_(e,t)},p:D,i:D,o:D,d(n){n&&v(e)}}}class tc extends ae{constructor(e){super(),le(this,e,null,ec,oe,{})}}const Vt={maxMS:9e5,initialMS:6e4,mult:2,maxSteps:4};class nc{constructor(e,t=Vt){W(this,"timerId",null);W(this,"currentMS");W(this,"step",0);W(this,"params");this.callback=e;const n={...t};n.maxMS<0&&(console.warn("PollingManager: Negative maxMS detected, using default value of 15 minutes"),n.maxMS=Vt.maxMS),n.initialMS<=0&&(console.warn("PollingManager: Negative or zero initialMS detected, using default value of 1 minute"),n.initialMS=Vt.initialMS),n.mult<=0&&(console.warn("PollingManager: Negative or zero multiplier detected, using default value of 2"),n.mult=Vt.mult),n.maxSteps!==void 0&&n.maxSteps<0&&(console.warn("PollingManager: Negative maxSteps detected, using default value of 4"),n.maxSteps=Vt.maxSteps),this.params=n,this.currentMS=this.params.maxMS}startPolling(){this.stopPolling(),this.currentMS=this.params.initialMS,this.step=0,this.safeExecute(),this.scheduleNext()}stopPolling(){this.timerId!==null&&(window.clearTimeout(this.timerId),this.timerId=null)}dispose(){this.stopPolling()}scheduleNext(){this.timerId=window.setTimeout(()=>{if(this.safeExecute(),this.params.maxMS===0){if(this.step++,this.params.maxSteps!==void 0&&this.step>=this.params.maxSteps)return void this.stopPolling()}else this.currentMS<this.params.maxMS&&(this.step++,this.params.maxSteps!==void 0&&this.step>=this.params.maxSteps?(this.currentMS=this.params.maxMS,this.step=0):this.currentMS=Math.min(this.currentMS*this.params.mult,this.params.maxMS));this.scheduleNext()},this.currentMS)}safeExecute(){try{const e=this.callback();e instanceof Promise&&e.catch(t=>console.error("Error in polling callback:",t))}catch(e){console.error("Error in polling callback:",e)}}}class is{constructor(e){W(this,"configs",ye([]));W(this,"pollingManager");W(this,"_enableDebugFeatures",ye(!1));W(this,"_settingsComponentSupported",ye({workspaceContext:!1,mcpServerList:!1,mcpServerImport:!1,orientation:!1,remoteTools:!1,userGuidelines:!1,terminal:!1,rules:!1}));W(this,"_enableAgentMode",ye(!1));W(this,"_enableAgentSwarmMode",ye(!1));W(this,"_enableNativeRemoteMcp",ye(!0));W(this,"_hasEverUsedRemoteAgent",ye(!1));W(this,"_enableInitialOrientation",ye(!1));W(this,"_userTier",ye("unknown"));W(this,"_guidelines",ye({}));this._host=e,this.pollingManager=new nc(()=>this.requestToolStatus(!1),{maxMS:0,initialMS:2e3,mult:1,maxSteps:150}),this.requestToolStatus(!1)}transformToolDisplay(e){const t=!e.isConfigured,n=e.oauthUrl;if(e.identifier.hostName===Rt.remoteToolHost){let s=e.identifier.toolId;switch(typeof s=="string"&&/^\d+$/.test(s)&&(s=Number(s)),s){case Te.GitHubApi:return{displayName:"GitHub",description:"Configure GitHub API access for repository operations",icon:mo,requiresAuthentication:t,authUrl:n};case Te.Linear:return{displayName:"Linear",description:"Configure Linear API access for issue tracking",icon:wo,requiresAuthentication:t,authUrl:n};case Te.Jira:return{displayName:"Jira",description:"Configure Jira API access for issue tracking",icon:To,requiresAuthentication:t,authUrl:n};case Te.Notion:return{displayName:"Notion",description:"Configure Notion API access",icon:yo,requiresAuthentication:t,authUrl:n};case Te.Confluence:return{displayName:"Confluence",description:"Configure Confluence API access",icon:No,requiresAuthentication:t,authUrl:n};case Te.WebSearch:return{displayName:"Web Search",description:"Configure web search capabilities",icon:vo,requiresAuthentication:t,authUrl:n};case Te.Supabase:return{displayName:"Supabase",description:"Configure Supabase API access",icon:ho,requiresAuthentication:t,authUrl:n};case Te.Glean:return{displayName:"Glean",description:"Configure Glean API access",icon:$o,requiresAuthentication:t,authUrl:n};case Te.Unknown:return{displayName:"Unknown",description:"Unknown tool",requiresAuthentication:t,authUrl:n};default:throw new Error(`Unhandled RemoteToolId: ${s}`)}}else if(e.identifier.hostName===Rt.localToolHost){const s=e.identifier.toolId;switch(s){case X.readFile:case X.editFile:case X.saveFile:case X.launchProcess:case X.killProcess:case X.readProcess:case X.writeProcess:case X.listProcesses:case X.waitProcess:case X.openBrowser:case X.clarify:case X.onboardingSubAgent:case X.strReplaceEditor:case X.remember:case X.diagnostics:case X.setupScript:case X.readTerminal:case X.gitCommitRetrieval:case X.memoryRetrieval:case X.startWorkerAgent:case X.readWorkerState:case X.waitForWorkerAgent:case X.sendInstructionToWorkerAgent:case X.stopWorkerAgent:case X.deleteWorkerAgent:case X.readWorkerAgentEdits:case X.LocalSubAgent:return{displayName:e.definition.name.toString(),description:"Local tool",icon:Ee,requiresAuthentication:t,authUrl:n};default:throw new Error(`Unhandled LocalToolType: ${s}`)}}else if(e.identifier.hostName===Rt.sidecarToolHost){const s=e.identifier.toolId;switch(s){case $e.codebaseRetrieval:return{displayName:"Code Search",description:"Configure codebase search capabilities",icon:Ve,requiresAuthentication:t,authUrl:n};case $e.shell:return{displayName:"Shell",description:"Shell",icon:Ve,requiresAuthentication:t,authUrl:n};case $e.strReplaceEditor:return{displayName:"File Edit",description:"File Editor",icon:Ve,requiresAuthentication:t,authUrl:n};case $e.view:return{displayName:"File View",description:"File Viewer",icon:Ve,requiresAuthentication:t,authUrl:n};case $e.webFetch:return{displayName:"Web Fetch",description:"Retrieve information from the web",icon:Ve,requiresAuthentication:t,authUrl:n};case $e.removeFiles:return{displayName:"Remove Files",description:"Remove files from the codebase",icon:Eo,requiresAuthentication:t,authUrl:n};case $e.remember:return{displayName:e.definition.name.toString(),description:"Remember",icon:Ee,requiresAuthentication:t,authUrl:n};case $e.saveFile:return{displayName:"Save File",description:"Save a new file",icon:xo,requiresAuthentication:t,authUrl:n};case $e.viewTaskList:return{displayName:"View Task List",description:"View the current task list",icon:Ee,requiresAuthentication:t,authUrl:n};case $e.reorganizeTaskList:return{displayName:"Reorganize Task List",description:"Reorganize the task list structure for major restructuring",icon:Ee,requiresAuthentication:t,authUrl:n};case $e.viewRangeUntruncated:return{displayName:e.definition.name.toString(),description:"View Range",icon:Ee,requiresAuthentication:t,authUrl:n};case $e.updateTasks:return{displayName:"Update Tasks",description:"Update one or more tasks in the task list",icon:Ee,requiresAuthentication:t,authUrl:n};case $e.addTasks:return{displayName:"Add Tasks",description:"Add one or more new tasks to the task list",icon:Ee,requiresAuthentication:t,authUrl:n};case $e.searchUntruncated:return{displayName:e.definition.name.toString(),description:"Search Untruncated",icon:Ee,requiresAuthentication:t,authUrl:n};case $e.renderMermaid:return{displayName:"View Mermaid Diagram",description:"View a mermaid diagram",icon:So,requiresAuthentication:t,authUrl:n};case $e.grepSearch:return{displayName:"Grep search",description:"Run grep search",icon:Ve,requiresAuthentication:t,authUrl:n};default:throw new Error(`Unhandled SidecarToolType: ${s}`)}}return{displayName:e.definition.name.toString(),description:e.definition.description||"",requiresAuthentication:t,authUrl:n}}handleMessageFromExtension(e){const t=e.data;switch(t.type){case ue.toolConfigInitialize:return this.createConfigsFromHostTools(t.data.hostTools,t.data.toolConfigs),t.data&&t.data.enableDebugFeatures!==void 0&&this._enableDebugFeatures.set(t.data.enableDebugFeatures),t.data&&t.data.settingsComponentSupported!==void 0&&this._settingsComponentSupported.set(t.data.settingsComponentSupported),t.data.enableAgentMode!==void 0&&this._enableAgentMode.set(t.data.enableAgentMode),t.data.enableAgentSwarmMode!==void 0&&this._enableAgentSwarmMode.set(t.data.enableAgentSwarmMode),t.data.hasEverUsedRemoteAgent!==void 0&&this._hasEverUsedRemoteAgent.set(t.data.hasEverUsedRemoteAgent),t.data.enableInitialOrientation!==void 0&&this._enableInitialOrientation.set(t.data.enableInitialOrientation),t.data.userTier!==void 0&&this._userTier.set(t.data.userTier),t.data.guidelines!==void 0&&this._guidelines.set(t.data.guidelines),t.data.enableNativeRemoteMcp!==void 0&&this._enableNativeRemoteMcp.set(t.data.enableNativeRemoteMcp),!0;case ue.toolConfigDefinitionsResponse:return this.configs.update(n=>this.createConfigsFromHostTools(t.data.hostTools,[]).map(s=>{const o=n.find(i=>i.name===s.name);return o?{...o,displayName:s.displayName,description:s.description,icon:s.icon,requiresAuthentication:s.requiresAuthentication,authUrl:s.authUrl,isConfigured:s.isConfigured,toolApprovalConfig:s.toolApprovalConfig}:s})),!0}return!1}createConfigsFromHostTools(e,t){return e.map(n=>{const s=this.transformToolDisplay(n),o=t.find(l=>l.name===n.definition.name),i=(o==null?void 0:o.isConfigured)??!s.requiresAuthentication;return{config:(o==null?void 0:o.config)??{},configString:JSON.stringify((o==null?void 0:o.config)??{},null,2),isConfigured:i,name:n.definition.name.toString(),displayName:s.displayName,description:s.description,identifier:n.identifier,icon:s.icon,requiresAuthentication:s.requiresAuthentication,authUrl:s.authUrl,showStatus:!1,statusMessage:"",statusType:"info",toolApprovalConfig:n.toolApprovalConfig}})}getConfigs(){return this.configs}isDisplayableTool(e){return["github","linear","notion","jira","confluence","supabase"].includes(e.displayName.toLowerCase())}getDisplayableTools(){return fn(this.configs,e=>{const t=e.filter(s=>this.isDisplayableTool(s)),n=new Map;for(const s of t)n.set(s.displayName,s);return Array.from(n.values()).sort((s,o)=>{const i={GitHub:1,Linear:2,Notion:3},l=Number.MAX_SAFE_INTEGER,a=i[s.displayName]||l,c=i[o.displayName]||l;return a<l&&c<l||a===l&&c===l?a!==c?a-c:s.displayName.localeCompare(o.displayName):a-c})})}getPretendNativeToolDefs(){return fn(this.configs,e=>this.getEnableNativeRemoteMcp()?Co(e):[])}saveConfig(e){this.startPolling()}notifyLoaded(){this._host.postMessage({type:ue.toolConfigLoaded})}startPolling(){this.pollingManager.startPolling()}requestToolStatus(e=!0){this._host.postMessage({type:ue.toolConfigGetDefinitions,data:{useCache:e}})}dispose(){this.pollingManager.dispose()}getEnableDebugFeatures(){return this._enableDebugFeatures}getEnableAgentMode(){return this._enableAgentMode}getEnableAgentSwarmMode(){return this._enableAgentSwarmMode}getEnableNativeRemoteMcp(){return this._host.clientType==="vscode"&&this._enableNativeRemoteMcp}getHasEverUsedRemoteAgent(){return this._hasEverUsedRemoteAgent}getEnableInitialOrientation(){return this._enableInitialOrientation}getUserTier(){return this._userTier}getGuidelines(){return this._guidelines}updateLocalUserGuidelines(e){this._guidelines.update(t=>t.userGuidelines?{...t,userGuidelines:{...t.userGuidelines,contents:e,enabled:e.length>0}}:t)}updateToolApprovalConfig(e,t){this.configs.update(n=>n.map(s=>s.identifier.toolId===e.toolId&&s.identifier.hostName===e.hostName?{...s,toolApprovalConfig:t}:s))}getSettingsComponentSupported(){return this._settingsComponentSupported}}W(is,"key","toolConfigModel");const sc=r=>({}),pr=r=>({}),rc=r=>({}),mr=r=>({});function oc(r){let e;const t=r[8]["header-left"],n=Oe(t,r,r[10],mr);return{c(){n&&n.c()},m(s,o){n&&n.m(s,o),e=!0},p(s,o){n&&n.p&&(!e||1024&o)&&ze(n,t,s,s[10],e?Ze(t,s[10],o,rc):Fe(s[10]),mr)},i(s){e||(m(n,s),e=!0)},o(s){g(n,s),e=!1},d(s){n&&n.d(s)}}}function ic(r){let e,t,n,s=r[0]&&fr(r),o=r[1]&&gr(r);return{c(){s&&s.c(),e=N(),o&&o.c(),t=ve()},m(i,l){s&&s.m(i,l),y(i,e,l),o&&o.m(i,l),y(i,t,l),n=!0},p(i,l){i[0]?s?(s.p(i,l),1&l&&m(s,1)):(s=fr(i),s.c(),m(s,1),s.m(e.parentNode,e)):s&&(j(),g(s,1,1,()=>{s=null}),V()),i[1]?o?(o.p(i,l),2&l&&m(o,1)):(o=gr(i),o.c(),m(o,1),o.m(t.parentNode,t)):o&&(j(),g(o,1,1,()=>{o=null}),V())},i(i){n||(m(s),m(o),n=!0)},o(i){g(s),g(o),n=!1},d(i){i&&(v(e),v(t)),s&&s.d(i),o&&o.d(i)}}}function fr(r){let e,t,n;var s=r[0];return s&&(t=mt(s,{})),{c(){e=k("div"),t&&S(t.$$.fragment),w(e,"class","icon-wrapper svelte-13uht7n")},m(o,i){y(o,e,i),t&&x(t,e,null),n=!0},p(o,i){if(1&i&&s!==(s=o[0])){if(t){j();const l=t;g(l.$$.fragment,1,0,()=>{C(l,1)}),V()}s?(t=mt(s,{}),S(t.$$.fragment),m(t.$$.fragment,1),x(t,e,null)):t=null}},i(o){n||(t&&m(t.$$.fragment,o),n=!0)},o(o){t&&g(t.$$.fragment,o),n=!1},d(o){o&&v(e),t&&C(t)}}}function gr(r){let e,t;return e=new Q({props:{color:"neutral",size:1,weight:"light",class:"card-title",$$slots:{default:[ac]},$$scope:{ctx:r}}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,s){const o={};1026&s&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function ac(r){let e;return{c(){e=R(r[1])},m(t,n){y(t,e,n)},p(t,n){2&n&&de(e,t[1])},d(t){t&&v(e)}}}function $r(r){let e,t;const n=r[8].default,s=Oe(n,r,r[10],null);return{c(){e=k("div"),s&&s.c(),w(e,"class","settings-card-body")},m(o,i){y(o,e,i),s&&s.m(e,null),t=!0},p(o,i){s&&s.p&&(!t||1024&i)&&ze(s,n,o,o[10],t?Ze(n,o[10],i,null):Fe(o[10]),null)},i(o){t||(m(s,o),t=!0)},o(o){g(s,o),t=!1},d(o){o&&v(e),s&&s.d(o)}}}function lc(r){let e,t,n,s,o,i,l,a,c,d,u;const f=[ic,oc],p=[];function h(M,T){return M[0]||M[1]?0:1}s=h(r),o=p[s]=f[s](r);const $=r[8]["header-right"],b=Oe($,r,r[10],pr);let E=r[5].default&&$r(r),I=[{role:"button"},{class:r[3]},r[4]],A={};for(let M=0;M<I.length;M+=1)A=Ae(A,I[M]);return{c(){e=k("div"),t=k("div"),n=k("div"),o.c(),i=N(),l=k("div"),b&&b.c(),a=N(),E&&E.c(),w(n,"class","settings-card-left svelte-13uht7n"),w(l,"class","settings-card-right svelte-13uht7n"),w(t,"class","settings-card-content svelte-13uht7n"),gn(e,A),he(e,"clickable",r[2]),he(e,"svelte-13uht7n",!0)},m(M,T){y(M,e,T),_(e,t),_(t,n),p[s].m(n,null),_(t,i),_(t,l),b&&b.m(l,null),_(e,a),E&&E.m(e,null),c=!0,d||(u=Ie(e,"click",r[9]),d=!0)},p(M,[T]){let O=s;s=h(M),s===O?p[s].p(M,T):(j(),g(p[O],1,1,()=>{p[O]=null}),V(),o=p[s],o?o.p(M,T):(o=p[s]=f[s](M),o.c()),m(o,1),o.m(n,null)),b&&b.p&&(!c||1024&T)&&ze(b,$,M,M[10],c?Ze($,M[10],T,sc):Fe(M[10]),pr),M[5].default?E?(E.p(M,T),32&T&&m(E,1)):(E=$r(M),E.c(),m(E,1),E.m(e,null)):E&&(j(),g(E,1,1,()=>{E=null}),V()),gn(e,A=pt(I,[{role:"button"},(!c||8&T)&&{class:M[3]},16&T&&M[4]])),he(e,"clickable",M[2]),he(e,"svelte-13uht7n",!0)},i(M){c||(m(o),m(b,M),m(E),c=!0)},o(M){g(o),g(b,M),g(E),c=!1},d(M){M&&v(e),p[s].d(),b&&b.d(M),E&&E.d(),d=!1,u()}}}function cc(r,e,t){let n,s,o;const i=["class","icon","title","isClickable"];let l=$n(e,i),{$$slots:a={},$$scope:c}=e;const d=lo(a);let{class:u=""}=e,{icon:f}=e,{title:p}=e,{isClickable:h=!1}=e;return r.$$set=$=>{e=Ae(Ae({},e),We($)),t(11,l=$n(e,i)),"class"in $&&t(6,u=$.class),"icon"in $&&t(0,f=$.icon),"title"in $&&t(1,p=$.title),"isClickable"in $&&t(2,h=$.isClickable),"$$scope"in $&&t(10,c=$.$$scope)},r.$$.update=()=>{t(7,{class:n,...s}=l,n,(t(4,s),t(11,l))),192&r.$$.dirty&&t(3,o=`settings-card ${u} ${n||""}`)},[f,p,h,o,s,d,u,n,a,function($){co.call(this,r,$)},c]}class wt extends ae{constructor(e){super(),le(this,e,cc,lc,oe,{class:6,icon:0,title:1,isClickable:2})}}function dc(r){let e,t,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 17 16"},r[0]],s={};for(let o=0;o<n.length;o+=1)s=Ae(s,n[o]);return{c(){e=_e("svg"),t=new Mn(!0),this.h()},l(o){e=An(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=Nn(e);t=Tn(i,!0),i.forEach(v),this.h()},h(){t.a=null,ft(e,s)},m(o,i){En(o,e,i),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M3.552 7.158a.568.568 0 0 1 .804 0l.702.702L6.23 6.688c.2-.2.511-.22.734-.06l.07.06a.568.568 0 0 1 0 .804L5.862 8.664l1.626 1.626 1.173-1.172c.2-.2.511-.22.733-.06l.071.06a.568.568 0 0 1 0 .804l-1.173 1.172.703.703c.2.2.22.51.06.733l-.06.07a.568.568 0 0 1-.804 0l-.041-.039-.812.813a3.226 3.226 0 0 1-4.043.421l-.08-.054-.959.96c-.2.2-.511.22-.733.06l-.071-.06a.568.568 0 0 1 0-.804l.96-.96-.054-.079a3.226 3.226 0 0 1 .294-3.91l.127-.133.811-.813-.038-.04a.567.567 0 0 1-.06-.734zm3.759-3.759a.568.568 0 0 1 .804 0l.038.04.815-.813a3.226 3.226 0 0 1 4.043-.421l.078.054.96-.96c.2-.2.511-.22.734-.06l.07.06a.568.568 0 0 1 0 .804l-.96.96.055.079a3.226 3.226 0 0 1-.295 3.91l-.126.133-.814.813.04.04c.201.2.221.511.06.734l-.06.07a.568.568 0 0 1-.804 0L7.31 4.204a.568.568 0 0 1 0-.805m2.39-.04-.884.884 3.093 3.093.884-.884A2.186 2.186 0 1 0 9.7 3.359M4.396 8.664l-.884.884a2.186 2.186 0 1 0 3.092 3.093l.884-.884z"/>',e)},p(o,[i]){ft(e,s=pt(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 17 16"},1&i&&o[0]]))},i:D,o:D,d(o){o&&v(e)}}}function uc(r,e,t){return r.$$set=n=>{t(0,e=Ae(Ae({},e),We(n)))},[e=We(e)]}class Ho extends ae{constructor(e){super(),le(this,e,uc,dc,oe,{})}}function pc(r){let e,t,n,s,o,i,l,a;return o=new rt({props:{triggerOn:[ns.Hover],content:"Revoke Access",$$slots:{default:[gc]},$$scope:{ctx:r}}}),l=new es.Root({props:{color:"success",size:1,variant:"soft",$$slots:{default:[$c]},$$scope:{ctx:r}}}),{c(){e=k("div"),t=k("div"),n=k("div"),s=k("div"),S(o.$$.fragment),i=N(),S(l.$$.fragment),w(s,"class","icon-button-wrapper svelte-js5lik"),he(s,"active",r[3]),w(n,"class","connection-status svelte-js5lik"),w(t,"class","icon-container svelte-js5lik"),w(e,"class","status-controls svelte-js5lik")},m(c,d){y(c,e,d),_(e,t),_(t,n),_(n,s),x(o,s,null),_(t,i),x(l,t,null),a=!0},p(c,d){const u={};2051&d&&(u.$$scope={dirty:d,ctx:c}),o.$set(u),(!a||8&d)&&he(s,"active",c[3]);const f={};2048&d&&(f.$$scope={dirty:d,ctx:c}),l.$set(f)},i(c){a||(m(o.$$.fragment,c),m(l.$$.fragment,c),a=!0)},o(c){g(o.$$.fragment,c),g(l.$$.fragment,c),a=!1},d(c){c&&v(e),C(o),C(l)}}}function mc(r){let e,t;return e=new we({props:{variant:"ghost-block",color:r[2]?"neutral":"accent",size:1,$$slots:{default:[yc]},$$scope:{ctx:r}}}),e.$on("click",r[4]),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,s){const o={};4&s&&(o.color=n[2]?"neutral":"accent"),2052&s&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function fc(r){let e,t;return e=new Ho({}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function gc(r){let e,t;return e=new Zt({props:{color:"neutral",variant:"ghost",size:1,$$slots:{default:[fc]},$$scope:{ctx:r}}}),e.$on("click",r[8]),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,s){const o={};2048&s&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function $c(r){let e;return{c(){e=R("Connected")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function hc(r){let e;return{c(){e=k("span"),e.textContent="Connect"},m(t,n){y(t,e,n)},i:D,o:D,d(t){t&&v(e)}}}function vc(r){let e,t,n,s,o;return t=new Xn({props:{size:1,useCurrentColor:!0}}),{c(){e=k("div"),S(t.$$.fragment),n=N(),s=k("span"),s.textContent="Cancel",w(e,"class","connect-button-spinner svelte-js5lik")},m(i,l){y(i,e,l),x(t,e,null),y(i,n,l),y(i,s,l),o=!0},i(i){o||(m(t.$$.fragment,i),o=!0)},o(i){g(t.$$.fragment,i),o=!1},d(i){i&&(v(e),v(n),v(s)),C(t)}}}function yc(r){let e,t,n,s;const o=[vc,hc],i=[];function l(a,c){return a[2]?0:1}return t=l(r),n=i[t]=o[t](r),{c(){e=k("div"),n.c(),w(e,"class","connect-button-content svelte-js5lik")},m(a,c){y(a,e,c),i[t].m(e,null),s=!0},p(a,c){let d=t;t=l(a),t!==d&&(j(),g(i[d],1,1,()=>{i[d]=null}),V(),n=i[t],n||(n=i[t]=o[t](a),n.c()),m(n,1),n.m(e,null))},i(a){s||(m(n),s=!0)},o(a){g(n),s=!1},d(a){a&&v(e),i[t].d()}}}function wc(r){let e,t,n,s;const o=[mc,pc],i=[];function l(a,c){return!a[0].isConfigured&&a[0].authUrl?0:a[0].isConfigured?1:-1}return~(t=l(r))&&(n=i[t]=o[t](r)),{c(){e=k("div"),n&&n.c(),w(e,"slot","header-right")},m(a,c){y(a,e,c),~t&&i[t].m(e,null),s=!0},p(a,c){let d=t;t=l(a),t===d?~t&&i[t].p(a,c):(n&&(j(),g(i[d],1,1,()=>{i[d]=null}),V()),~t?(n=i[t],n?n.p(a,c):(n=i[t]=o[t](a),n.c()),m(n,1),n.m(e,null)):n=null)},i(a){s||(m(n),s=!0)},o(a){g(n),s=!1},d(a){a&&v(e),~t&&i[t].d()}}}function hr(r){let e,t,n,s=r[0].statusMessage+"";return{c(){e=k("div"),t=R(s),w(e,"class",n="status-message "+r[0].statusType+" svelte-js5lik")},m(o,i){y(o,e,i),_(e,t)},p(o,i){1&i&&s!==(s=o[0].statusMessage+"")&&de(t,s),1&i&&n!==(n="status-message "+o[0].statusType+" svelte-js5lik")&&w(e,"class",n)},d(o){o&&v(e)}}}function Sc(r){let e,t,n,s,o,i;t=new wt({props:{icon:r[0].icon,title:r[0].displayName,$$slots:{"header-right":[wc]},$$scope:{ctx:r}}});let l=r[0].showStatus&&hr(r);return{c(){e=k("div"),S(t.$$.fragment),n=N(),l&&l.c(),w(e,"class","config-wrapper"),w(e,"role","group"),w(e,"aria-label","Connection status controls")},m(a,c){y(a,e,c),x(t,e,null),_(e,n),l&&l.m(e,null),s=!0,o||(i=[Ie(e,"mouseenter",r[9]),Ie(e,"mouseleave",r[10])],o=!0)},p(a,[c]){const d={};1&c&&(d.icon=a[0].icon),1&c&&(d.title=a[0].displayName),2063&c&&(d.$$scope={dirty:c,ctx:a}),t.$set(d),a[0].showStatus?l?l.p(a,c):(l=hr(a),l.c(),l.m(e,null)):l&&(l.d(1),l=null)},i(a){s||(m(t.$$.fragment,a),s=!0)},o(a){g(t.$$.fragment,a),s=!1},d(a){a&&v(e),C(t),l&&l.d(),o=!1,kn(i)}}}function xc(r,e,t){let{config:n}=e,{onAuthenticate:s}=e,{onRevokeAccess:o}=e,i=!1,l=null,a=!1;return r.$$set=c=>{"config"in c&&t(0,n=c.config),"onAuthenticate"in c&&t(5,s=c.onAuthenticate),"onRevokeAccess"in c&&t(1,o=c.onRevokeAccess)},r.$$.update=()=>{133&r.$$.dirty&&n.isConfigured&&i&&(t(2,i=!1),l&&(clearTimeout(l),t(7,l=null)))},[n,o,i,a,function(){if(i)t(2,i=!1),l&&(clearTimeout(l),t(7,l=null));else{t(2,i=!0);const c=n.authUrl||"";s(c),t(7,l=setTimeout(()=>{t(2,i=!1),t(7,l=null)},6e4))}},s,()=>{},l,()=>o(n),()=>t(3,a=!0),()=>t(3,a=!1)]}class Cc extends ae{constructor(e){super(),le(this,e,xc,Sc,oe,{config:0,onAuthenticate:5,onRevokeAccess:1,onToolApprovalConfigChange:6})}get onToolApprovalConfigChange(){return this.$$.ctx[6]}}function _c(r){let e;return{c(){e=R(r[0])},m(t,n){y(t,e,n)},p(t,n){1&n&&de(e,t[0])},d(t){t&&v(e)}}}function bc(r){let e,t;const n=r[2].default,s=Oe(n,r,r[3],null);return{c(){e=k("div"),s&&s.c(),w(e,"class","category-content")},m(o,i){y(o,e,i),s&&s.m(e,null),t=!0},p(o,i){s&&s.p&&(!t||8&i)&&ze(s,n,o,o[3],t?Ze(n,o[3],i,null):Fe(o[3]),null)},i(o){t||(m(s,o),t=!0)},o(o){g(s,o),t=!1},d(o){o&&v(e),s&&s.d(o)}}}function kc(r){let e,t,n,s,o;return t=new Xn({props:{size:1}}),s=new Q({props:{size:1,color:"secondary",$$slots:{default:[Mc]},$$scope:{ctx:r}}}),{c(){e=k("div"),S(t.$$.fragment),n=N(),S(s.$$.fragment),w(e,"class","loading-container svelte-2bsejd")},m(i,l){y(i,e,l),x(t,e,null),_(e,n),x(s,e,null),o=!0},p(i,l){const a={};8&l&&(a.$$scope={dirty:l,ctx:i}),s.$set(a)},i(i){o||(m(t.$$.fragment,i),m(s.$$.fragment,i),o=!0)},o(i){g(t.$$.fragment,i),g(s.$$.fragment,i),o=!1},d(i){i&&v(e),C(t),C(s)}}}function Mc(r){let e;return{c(){e=R("Loading...")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Ac(r){let e,t,n,s,o,i,l;n=new Q({props:{size:1,color:"secondary",weight:"regular",$$slots:{default:[_c]},$$scope:{ctx:r}}});const a=[kc,bc],c=[];function d(u,f){return u[1]?0:1}return o=d(r),i=c[o]=a[o](r),{c(){e=k("div"),t=k("div"),S(n.$$.fragment),s=N(),i.c(),w(t,"class","category-heading"),w(e,"class","category")},m(u,f){y(u,e,f),_(e,t),x(n,t,null),_(e,s),c[o].m(e,null),l=!0},p(u,[f]){const p={};9&f&&(p.$$scope={dirty:f,ctx:u}),n.$set(p);let h=o;o=d(u),o===h?c[o].p(u,f):(j(),g(c[h],1,1,()=>{c[h]=null}),V(),i=c[o],i?i.p(u,f):(i=c[o]=a[o](u),i.c()),m(i,1),i.m(e,null))},i(u){l||(m(n.$$.fragment,u),m(i),l=!0)},o(u){g(n.$$.fragment,u),g(i),l=!1},d(u){u&&v(e),C(n),c[o].d()}}}function Nc(r,e,t){let{$$slots:n={},$$scope:s}=e,{title:o}=e,{loading:i=!1}=e;return r.$$set=l=>{"title"in l&&t(0,o=l.title),"loading"in l&&t(1,i=l.loading),"$$scope"in l&&t(3,s=l.$$scope)},[o,i,n,s]}class Tc extends ae{constructor(e){super(),le(this,e,Nc,Ac,oe,{title:0,loading:1})}}const Bo="extensionClient",Ec="mcpServerModel";function as(){const r=Ft(Ec);if(!r)throw new Error("MCPServerModel context not found. Make sure setMCPServerModelContext() was called in a parent component.");return r}function Ic(r){let e,t,n,s,o,i;return n=new rt({props:{triggerOn:[ns.Hover],content:"Revoke Access",$$slots:{default:[Lc]},$$scope:{ctx:r}}}),o=new es.Root({props:{color:"success",size:1,variant:"soft",$$slots:{default:[Oc]},$$scope:{ctx:r}}}),{c(){e=k("div"),t=k("div"),S(n.$$.fragment),s=N(),S(o.$$.fragment),w(t,"class","disconnect-button svelte-e3a21z"),he(t,"active",r[2]),w(e,"class","status-controls svelte-e3a21z")},m(l,a){y(l,e,a),_(e,t),x(n,t,null),_(e,s),x(o,e,null),i=!0},p(l,a){const c={};2048&a&&(c.$$scope={dirty:a,ctx:l}),n.$set(c),(!i||4&a)&&he(t,"active",l[2]);const d={};2048&a&&(d.$$scope={dirty:a,ctx:l}),o.$set(d)},i(l){i||(m(n.$$.fragment,l),m(o.$$.fragment,l),i=!0)},o(l){g(n.$$.fragment,l),g(o.$$.fragment,l),i=!1},d(l){l&&v(e),C(n),C(o)}}}function Rc(r){let e,t;return e=new we({props:{variant:"ghost-block",color:r[1]?"neutral":"accent",size:1,$$slots:{default:[Zc]},$$scope:{ctx:r}}}),e.$on("click",r[3]),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,s){const o={};2&s&&(o.color=n[1]?"neutral":"accent"),2050&s&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Pc(r){let e,t;return e=new Ho({}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Lc(r){let e,t;return e=new Zt({props:{color:"neutral",variant:"ghost",size:1,$$slots:{default:[Pc]},$$scope:{ctx:r}}}),e.$on("click",r[4]),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,s){const o={};2048&s&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Oc(r){let e;return{c(){e=R("Connected")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function zc(r){let e;return{c(){e=k("span"),e.textContent="Connect"},m(t,n){y(t,e,n)},i:D,o:D,d(t){t&&v(e)}}}function Fc(r){let e,t,n,s,o;return t=new Xn({props:{size:1,useCurrentColor:!0}}),{c(){e=k("div"),S(t.$$.fragment),n=N(),s=k("span"),s.textContent="Cancel",w(e,"class","connect-button-spinner svelte-e3a21z")},m(i,l){y(i,e,l),x(t,e,null),y(i,n,l),y(i,s,l),o=!0},i(i){o||(m(t.$$.fragment,i),o=!0)},o(i){g(t.$$.fragment,i),o=!1},d(i){i&&(v(e),v(n),v(s)),C(t)}}}function Zc(r){let e,t,n,s;const o=[Fc,zc],i=[];function l(a,c){return a[1]?0:1}return t=l(r),n=i[t]=o[t](r),{c(){e=k("div"),n.c(),w(e,"class","connect-button-content svelte-e3a21z")},m(a,c){y(a,e,c),i[t].m(e,null),s=!0},p(a,c){let d=t;t=l(a),t!==d&&(j(),g(i[d],1,1,()=>{i[d]=null}),V(),n=i[t],n||(n=i[t]=o[t](a),n.c()),m(n,1),n.m(e,null))},i(a){s||(m(n),s=!0)},o(a){g(n),s=!1},d(a){a&&v(e),i[t].d()}}}function Uc(r){let e,t,n,s;const o=[Rc,Ic],i=[];function l(a,c){return a[0].isConfigured?a[0].isConfigured?1:-1:0}return~(t=l(r))&&(n=i[t]=o[t](r)),{c(){e=k("div"),n&&n.c(),w(e,"slot","header-right")},m(a,c){y(a,e,c),~t&&i[t].m(e,null),s=!0},p(a,c){let d=t;t=l(a),t===d?~t&&i[t].p(a,c):(n&&(j(),g(i[d],1,1,()=>{i[d]=null}),V()),~t?(n=i[t],n?n.p(a,c):(n=i[t]=o[t](a),n.c()),m(n,1),n.m(e,null)):n=null)},i(a){s||(m(n),s=!0)},o(a){g(n),s=!1},d(a){a&&v(e),~t&&i[t].d()}}}function vr(r){let e,t,n,s=r[0].statusMessage+"";return{c(){e=k("div"),t=R(s),w(e,"class",n="status-message "+r[0].statusType+" svelte-e3a21z")},m(o,i){y(o,e,i),_(e,t)},p(o,i){1&i&&s!==(s=o[0].statusMessage+"")&&de(t,s),1&i&&n!==(n="status-message "+o[0].statusType+" svelte-e3a21z")&&w(e,"class",n)},d(o){o&&v(e)}}}function Dc(r){let e,t,n,s,o,i;t=new wt({props:{icon:r[0].icon,title:r[0].displayName,$$slots:{"header-right":[Uc]},$$scope:{ctx:r}}});let l=r[0].showStatus&&vr(r);return{c(){e=k("div"),S(t.$$.fragment),n=N(),l&&l.c(),w(e,"class","config-wrapper"),w(e,"role","group"),w(e,"aria-label","Connection status controls")},m(a,c){y(a,e,c),x(t,e,null),_(e,n),l&&l.m(e,null),s=!0,o||(i=[Ie(e,"mouseenter",r[6]),Ie(e,"mouseleave",r[7])],o=!0)},p(a,[c]){const d={};1&c&&(d.icon=a[0].icon),1&c&&(d.title=a[0].displayName),2055&c&&(d.$$scope={dirty:c,ctx:a}),t.$set(d),a[0].showStatus?l?l.p(a,c):(l=vr(a),l.c(),l.m(e,null)):l&&(l.d(1),l=null)},i(a){s||(m(t.$$.fragment,a),s=!0)},o(a){g(t.$$.fragment,a),s=!1},d(a){a&&v(e),C(t),l&&l.d(),o=!1,kn(i)}}}function jc(r,e,t){let{config:n}=e,{mcpTool:s}=e;const o=as(),i=function(){const d=Ft(Bo);if(!d)throw new Error("ExtensionClient context not found. Make sure setExtensionClientContext() was called in a parent component.");return d}();let l=!1,a=!1,c=null;return r.$$set=d=>{"config"in d&&t(0,n=d.config),"mcpTool"in d&&t(5,s=d.mcpTool)},r.$$.update=()=>{1&r.$$.dirty&&t(0,n=li(n)),32&r.$$.dirty&&t(0,n.isConfigured=!!s,n)},[n,l,a,async function(){if(l)return c&&(clearTimeout(c),c=null),void t(1,l=!1);i.startRemoteMCPAuth(n.name),t(1,l=!0);const d=new Promise(u=>{c=setTimeout(()=>{u(),c=null},6e4)});await Promise.race([d]),t(1,l=!1)},async function(){await i.openConfirmationModal({title:"Revoke Access",message:`Are you sure you want to revoke access for ${n.displayName}? This will disconnect the tool and you'll need to reconnect it to use it again.`,confirmButtonText:"Revoke Access",cancelButtonText:"Cancel"})&&(s&&o.deleteServer(s==null?void 0:s.id),t(1,l=!1))},s,()=>t(2,a=!0),()=>t(2,a=!1)]}class Vc extends ae{constructor(e){super(),le(this,e,jc,Dc,oe,{config:0,mcpTool:5})}}function yr(r,e,t){const n=r.slice();return n[13]=e[t],n}function wr(r,e,t){const n=r.slice();return n[13]=e[t],n}function Sr(r,e){let t,n,s;return n=new Cc({props:{config:e[13],onAuthenticate:e[2],onRevokeAccess:e[3],onToolApprovalConfigChange:e[4]}}),{key:r,first:null,c(){t=ve(),S(n.$$.fragment),this.first=t},m(o,i){y(o,t,i),x(n,o,i),s=!0},p(o,i){e=o;const l={};2&i&&(l.config=e[13]),4&i&&(l.onAuthenticate=e[2]),8&i&&(l.onRevokeAccess=e[3]),16&i&&(l.onToolApprovalConfigChange=e[4]),n.$set(l)},i(o){s||(m(n.$$.fragment,o),s=!0)},o(o){g(n.$$.fragment,o),s=!1},d(o){o&&v(t),C(n,o)}}}function xr(r,e){let t,n,s;function o(...i){return e[10](e[13],...i)}return n=new Vc({props:{mcpTool:e[5].find(o),config:e[13]}}),{key:r,first:null,c(){t=ve(),S(n.$$.fragment),this.first=t},m(i,l){y(i,t,l),x(n,i,l),s=!0},p(i,l){e=i;const a={};96&l&&(a.mcpTool=e[5].find(o)),64&l&&(a.config=e[13]),n.$set(a)},i(i){s||(m(n.$$.fragment,i),s=!0)},o(i){g(n.$$.fragment,i),s=!1},d(i){i&&v(t),C(n,i)}}}function qc(r){let e,t,n,s=[],o=new Map,i=[],l=new Map,a=ce(r[1]);const c=f=>f[13].name;for(let f=0;f<a.length;f+=1){let p=wr(r,a,f),h=c(p);o.set(h,s[f]=Sr(h,p))}let d=ce(r[6]);const u=f=>f[13].name;for(let f=0;f<d.length;f+=1){let p=yr(r,d,f),h=u(p);l.set(h,i[f]=xr(h,p))}return{c(){e=k("div");for(let f=0;f<s.length;f+=1)s[f].c();t=N();for(let f=0;f<i.length;f+=1)i[f].c();w(e,"class","tool-category-list svelte-on3wl5")},m(f,p){y(f,e,p);for(let h=0;h<s.length;h+=1)s[h]&&s[h].m(e,null);_(e,t);for(let h=0;h<i.length;h+=1)i[h]&&i[h].m(e,null);n=!0},p(f,p){30&p&&(a=ce(f[1]),j(),s=Ke(s,p,c,1,f,a,o,e,Ye,Sr,t,wr),V()),96&p&&(d=ce(f[6]),j(),i=Ke(i,p,u,1,f,d,l,e,Ye,xr,null,yr),V())},i(f){if(!n){for(let p=0;p<a.length;p+=1)m(s[p]);for(let p=0;p<d.length;p+=1)m(i[p]);n=!0}},o(f){for(let p=0;p<s.length;p+=1)g(s[p]);for(let p=0;p<i.length;p+=1)g(i[p]);n=!1},d(f){f&&v(e);for(let p=0;p<s.length;p+=1)s[p].d();for(let p=0;p<i.length;p+=1)i[p].d()}}}function Hc(r){let e,t,n;return t=new Tc({props:{title:r[0],loading:r[1].length===0,$$slots:{default:[qc]},$$scope:{ctx:r}}}),{c(){e=k("div"),S(t.$$.fragment)},m(s,o){y(s,e,o),x(t,e,null),n=!0},p(s,[o]){const i={};1&o&&(i.title=s[0]),2&o&&(i.loading=s[1].length===0),262270&o&&(i.$$scope={dirty:o,ctx:s}),t.$set(i)},i(s){n||(m(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&v(e),C(t)}}}function Bc(r,e,t){let n,s,o,{title:i}=e,{tools:l=[]}=e,{onAuthenticate:a}=e,{onRevokeAccess:c}=e,{onToolApprovalConfigChange:d=()=>{}}=e;const u=Ft(is.key),f=as(),p=u.getPretendNativeToolDefs();Pe(r,p,$=>t(6,o=$));const h=f.getServers();return Pe(r,h,$=>t(9,s=$)),r.$$set=$=>{"title"in $&&t(0,i=$.title),"tools"in $&&t(1,l=$.tools),"onAuthenticate"in $&&t(2,a=$.onAuthenticate),"onRevokeAccess"in $&&t(3,c=$.onRevokeAccess),"onToolApprovalConfigChange"in $&&t(4,d=$.onToolApprovalConfigChange)},r.$$.update=()=>{512&r.$$.dirty&&t(5,n=u.getEnableNativeRemoteMcp()?Co(s):[])},[i,l,a,c,d,n,o,p,h,s,($,b)=>b.name===$.name]}class Gc extends ae{constructor(e){super(),le(this,e,Bc,Hc,oe,{title:0,tools:1,onAuthenticate:2,onRevokeAccess:3,onToolApprovalConfigChange:4})}}class Ln{constructor(e){W(this,"servers",ye([]));this.host=e,this.loadServersFromStorage()}handleMessageFromExtension(e){const t=e.data;if(t.type===ue.getStoredMCPServersResponse){const n=t.data;return Array.isArray(n)&&this.servers.set(n),!0}return!1}async importServersFromJSON(e){return this.importFromJSON(e)}loadServersFromStorage(){try{this.host.postMessage({type:ue.getStoredMCPServers})}catch(e){console.error("Failed to load MCP servers:",e),this.servers.set([])}}saveServers(e){try{this.host.postMessage({type:ue.setStoredMCPServers,data:e})}catch(t){throw console.error("Failed to save MCP servers:",t),new Ce("Failed to save MCP servers")}}getServers(){return this.servers}addServer(e){this.checkExistingServerName(e.name),this.servers.update(t=>{const n=[...t,{...e,id:crypto.randomUUID()}];return this.saveServers(n),n})}addServers(e){for(const t of e)this.checkExistingServerName(t.name);this.servers.update(t=>{const n=[...t,...e.map(s=>({...s,id:crypto.randomUUID()}))];return this.saveServers(n),n})}checkExistingServerName(e,t){const n=dt(this.servers).find(s=>s.name===e);if(n&&(n==null?void 0:n.id)!==t)throw new Ce(`Server name '${e}' already exists`)}updateServer(e){this.checkExistingServerName(e.name,e.id),this.servers.update(t=>{const n=t.map(s=>s.id===e.id?e:s);return this.saveServers(n),n})}deleteServer(e){this.servers.update(t=>{const n=t.filter(s=>s.id!==e);return this.saveServers(n),n})}toggleDisabledServer(e){this.servers.update(t=>{const n=t.map(s=>s.id===e?{...s,disabled:!s.disabled}:s);return this.saveServers(n),n})}static convertServerToJSON(e){if(je(e))return JSON.stringify({mcpServers:{[e.name]:{url:e.url,type:e.type}}},null,2);{const t=e;return JSON.stringify({mcpServers:{[t.name]:{command:t.command.split(" ")[0],args:t.command.split(" ").slice(1),env:t.env}}},null,2)}}static parseServerValidationMessages(e){const t=new Map,n=new Map;e.forEach(o=>{var l,a;const i=(l=o.tools)==null?void 0:l.filter(c=>!c.enabled).map(c=>c.definition.mcp_tool_name);o.disabled?t.set(o.id,"MCP server has been manually disabled"):o.tools&&o.tools.length===0?t.set(o.id,"No tools are available for this MCP server"):i&&i.length===((a=o.tools)==null?void 0:a.length)?t.set(o.id,"All tools for this MCP server have validation errors: "+i.join(", ")):i&&i.length>0&&n.set(o.id,"MCP server has validation errors in the following tools which have been disabled: "+i.join(", "))});const s=this.parseDuplicateServerIds(e);return{errors:new Map([...t,...s]),warnings:n}}static parseDuplicateServerIds(e){const t=new Map;for(const s of e)t.has(s.name)||t.set(s.name,[]),t.get(s.name).push(s.id);const n=new Map;for(const[,s]of t)if(s.length>1)for(let o=1;o<s.length;o++)n.set(s[o],"MCP server is disabled due to duplicate server names");return n}static convertParsedServerToWebview(e){const{tools:t,...n}=e;return{...n,tools:void 0}}static parseServerConfigFromJSON(e){return Fo(e).map(t=>this.convertParsedServerToWebview(t))}importFromJSON(e){try{const t=Ln.parseServerConfigFromJSON(e),n=dt(this.servers),s=new Set(n.map(o=>o.name));for(const o of t){if(!o.name)throw new Ce("All servers must have a name.");if(s.has(o.name))throw new Ce(`A server with the name '${o.name}' already exists.`);s.add(o.name)}return this.servers.update(o=>{const i=[...o,...t.map(l=>({...l,id:crypto.randomUUID()}))];return this.saveServers(i),i}),t.length}catch(t){throw t instanceof Ce?t:new Ce("Failed to import MCP servers from JSON. Please check the format.")}}}function Cr(r,e,t){const n=r.slice();return n[11]=e[t],n[12]=e,n[13]=t,n}function Jc(r){let e;return{c(){e=R("Environment Variables")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function _r(r){let e,t,n=[],s=new Map,o=ce(r[0]);const i=l=>l[11].id;for(let l=0;l<o.length;l+=1){let a=Cr(r,o,l),c=i(a);s.set(c,n[l]=br(c,a))}return{c(){for(let l=0;l<n.length;l+=1)n[l].c();e=ve()},m(l,a){for(let c=0;c<n.length;c+=1)n[c]&&n[c].m(l,a);y(l,e,a),t=!0},p(l,a){59&a&&(o=ce(l[0]),j(),n=Ke(n,a,i,1,l,o,s,e.parentNode,Ye,br,e,Cr),V())},i(l){if(!t){for(let a=0;a<o.length;a+=1)m(n[a]);t=!0}},o(l){for(let a=0;a<n.length;a+=1)g(n[a]);t=!1},d(l){l&&v(e);for(let a=0;a<n.length;a+=1)n[a].d(l)}}}function Wc(r){let e,t;return e=new _o({props:{slot:"iconLeft"}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p:D,i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Kc(r){let e,t;return e=new we({props:{variant:"ghost",color:"neutral",type:"button",size:1,$$slots:{iconLeft:[Wc]},$$scope:{ctx:r}}}),e.$on("focus",function(){_t(r[1])&&r[1].apply(this,arguments)}),e.$on("click",function(){return r[10](r[11])}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,s){r=n;const o={};16384&s&&(o.$$scope={dirty:s,ctx:r}),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function br(r,e){let t,n,s,o,i,l,a,c,d,u,f,p,h;function $(A){e[6](A,e[11])}let b={size:1,placeholder:"Name",class:"full-width"};function E(A){e[8](A,e[11])}e[11].key!==void 0&&(b.value=e[11].key),s=new bt({props:b}),be.push(()=>ke(s,"value",$)),s.$on("focus",function(){_t(e[1])&&e[1].apply(this,arguments)}),s.$on("change",function(){return e[7](e[11])});let I={size:1,placeholder:"Value",class:"full-width"};return e[11].value!==void 0&&(I.value=e[11].value),a=new bt({props:I}),be.push(()=>ke(a,"value",E)),a.$on("focus",function(){_t(e[1])&&e[1].apply(this,arguments)}),a.$on("change",function(){return e[9](e[11])}),f=new rt({props:{content:"Remove",$$slots:{default:[Kc]},$$scope:{ctx:e}}}),{key:r,first:null,c(){t=k("tr"),n=k("td"),S(s.$$.fragment),i=N(),l=k("td"),S(a.$$.fragment),d=N(),u=k("td"),S(f.$$.fragment),p=N(),w(n,"class","name-cell svelte-1mazg1z"),w(l,"class","value-cell svelte-1mazg1z"),w(u,"class","action-cell svelte-1mazg1z"),w(t,"class","env-var-row svelte-1mazg1z"),this.first=t},m(A,M){y(A,t,M),_(t,n),x(s,n,null),_(t,i),_(t,l),x(a,l,null),_(t,d),_(t,u),x(f,u,null),_(t,p),h=!0},p(A,M){e=A;const T={};!o&&1&M&&(o=!0,T.value=e[11].key,Me(()=>o=!1)),s.$set(T);const O={};!c&&1&M&&(c=!0,O.value=e[11].value,Me(()=>c=!1)),a.$set(O);const G={};16387&M&&(G.$$scope={dirty:M,ctx:e}),f.$set(G)},i(A){h||(m(s.$$.fragment,A),m(a.$$.fragment,A),m(f.$$.fragment,A),h=!0)},o(A){g(s.$$.fragment,A),g(a.$$.fragment,A),g(f.$$.fragment,A),h=!1},d(A){A&&v(t),C(s),C(a),C(f)}}}function Yc(r){let e;return{c(){e=R("Variable")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Xc(r){let e,t;return e=new ln({props:{slot:"iconLeft"}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p:D,i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Qc(r){let e,t,n,s,o,i,l,a;e=new Q({props:{size:1,weight:"medium",$$slots:{default:[Jc]},$$scope:{ctx:r}}});let c=r[0].length>0&&_r(r);return l=new we({props:{size:1,variant:"soft",color:"neutral",type:"button",$$slots:{iconLeft:[Xc],default:[Yc]},$$scope:{ctx:r}}}),l.$on("click",r[2]),{c(){S(e.$$.fragment),t=N(),n=k("table"),s=k("tbody"),c&&c.c(),o=N(),i=k("div"),S(l.$$.fragment),w(n,"class","env-vars-table svelte-1mazg1z"),w(i,"class","new-var-button-container svelte-1mazg1z")},m(d,u){x(e,d,u),y(d,t,u),y(d,n,u),_(n,s),c&&c.m(s,null),y(d,o,u),y(d,i,u),x(l,i,null),a=!0},p(d,[u]){const f={};16384&u&&(f.$$scope={dirty:u,ctx:d}),e.$set(f),d[0].length>0?c?(c.p(d,u),1&u&&m(c,1)):(c=_r(d),c.c(),m(c,1),c.m(s,null)):c&&(j(),g(c,1,1,()=>{c=null}),V());const p={};16384&u&&(p.$$scope={dirty:u,ctx:d}),l.$set(p)},i(d){a||(m(e.$$.fragment,d),m(c),m(l.$$.fragment,d),a=!0)},o(d){g(e.$$.fragment,d),g(c),g(l.$$.fragment,d),a=!1},d(d){d&&(v(t),v(n),v(o),v(i)),C(e,d),c&&c.d(),C(l)}}}function ed(r,e,t){let{handleEnterEditMode:n}=e,{envVarEntries:s=[]}=e;function o(a){n(),t(0,s=s.filter(c=>c.id!==a))}function i(a,c){const d=s.findIndex(u=>u.id===a);d!==-1&&(t(0,s[d].key=c,s),t(0,s))}function l(a,c){const d=s.findIndex(u=>u.id===a);d!==-1&&(t(0,s[d].value=c,s),t(0,s))}return r.$$set=a=>{"handleEnterEditMode"in a&&t(1,n=a.handleEnterEditMode),"envVarEntries"in a&&t(0,s=a.envVarEntries)},[s,n,function(){n(),t(0,s=[...s,{id:crypto.randomUUID(),key:"",value:""}])},o,i,l,function(a,c){r.$$.not_equal(c.key,a)&&(c.key=a,t(0,s))},a=>i(a.id,a.key),function(a,c){r.$$.not_equal(c.value,a)&&(c.value=a,t(0,s))},a=>l(a.id,a.value),a=>o(a.id)]}class td extends ae{constructor(e){super(),le(this,e,ed,Qc,oe,{handleEnterEditMode:1,envVarEntries:0})}}function kr(r,e,t){const n=r.slice();return n[45]=e[t],n}function nd(r){let e,t,n,s,o,i,l,a,c,d,u,f,p,h,$,b,E,I,A,M,T,O,G,ne,z,Z=(r[0]==="add"||r[0]==="edit")&&!je(r[3]);i=new ci({}),a=new Q({props:{color:"secondary",size:1,weight:"medium",$$slots:{default:[rd]},$$scope:{ctx:r}}});const P=[id,od],H=[];function ie(J,U){return J[0]==="addJson"?0:J[0]==="add"||J[0]==="addRemote"||J[0]==="edit"?1:-1}~(d=ie(r))&&(u=H[d]=P[d](r));let F=Z&&Ar(r);return b=new In({props:{variant:"soft",color:"error",size:1,$$slots:{icon:[wd],default:[yd]},$$scope:{ctx:r}}}),A=new we({props:{size:1,variant:"ghost",color:"neutral",type:"button",$$slots:{default:[Sd]},$$scope:{ctx:r}}}),A.$on("click",r[22]),T=new we({props:{size:1,variant:"solid",color:"accent",loading:r[2],type:"submit",disabled:r[17],$$slots:{default:[kd]},$$scope:{ctx:r}}}),{c(){e=k("form"),t=k("div"),n=k("div"),s=k("div"),o=k("div"),S(i.$$.fragment),l=N(),S(a.$$.fragment),c=N(),u&&u.c(),f=N(),F&&F.c(),p=N(),h=k("div"),$=k("div"),S(b.$$.fragment),E=N(),I=k("div"),S(A.$$.fragment),M=N(),S(T.$$.fragment),w(o,"class","server-icon svelte-igdbzh"),w(s,"class","server-title svelte-igdbzh"),w(n,"class","server-header svelte-igdbzh"),w($,"class","error-container svelte-igdbzh"),he($,"is-error",!!r[1]),w(I,"class","form-actions svelte-igdbzh"),w(h,"class","form-actions-row svelte-igdbzh"),w(t,"class","server-edit-form svelte-igdbzh"),w(e,"class",O="c-mcp-server-card "+(r[0]==="add"||r[0]==="addJson"||r[0]==="addRemote"?"add-server-section":"server-item")+" svelte-igdbzh")},m(J,U){y(J,e,U),_(e,t),_(t,n),_(n,s),_(s,o),x(i,o,null),_(s,l),x(a,s,null),_(t,c),~d&&H[d].m(t,null),_(t,f),F&&F.m(t,null),_(t,p),_(t,h),_(h,$),x(b,$,null),_(h,E),_(h,I),x(A,I,null),_(I,M),x(T,I,null),G=!0,ne||(z=Ie(e,"submit",ei(r[21])),ne=!0)},p(J,U){const fe={};65536&U[0]|131072&U[1]&&(fe.$$scope={dirty:U,ctx:J}),a.$set(fe);let De=d;d=ie(J),d===De?~d&&H[d].p(J,U):(u&&(j(),g(H[De],1,1,()=>{H[De]=null}),V()),~d?(u=H[d],u?u.p(J,U):(u=H[d]=P[d](J),u.c()),m(u,1),u.m(t,f)):u=null),9&U[0]&&(Z=(J[0]==="add"||J[0]==="edit")&&!je(J[3])),Z?F?(F.p(J,U),9&U[0]&&m(F,1)):(F=Ar(J),F.c(),m(F,1),F.m(t,p)):F&&(j(),g(F,1,1,()=>{F=null}),V());const Nt={};2&U[0]|131072&U[1]&&(Nt.$$scope={dirty:U,ctx:J}),b.$set(Nt),(!G||2&U[0])&&he($,"is-error",!!J[1]);const Ut={};131072&U[1]&&(Ut.$$scope={dirty:U,ctx:J}),A.$set(Ut);const St={};4&U[0]&&(St.loading=J[2]),131072&U[0]&&(St.disabled=J[17]),1&U[0]|131072&U[1]&&(St.$$scope={dirty:U,ctx:J}),T.$set(St),(!G||1&U[0]&&O!==(O="c-mcp-server-card "+(J[0]==="add"||J[0]==="addJson"||J[0]==="addRemote"?"add-server-section":"server-item")+" svelte-igdbzh"))&&w(e,"class",O)},i(J){G||(m(i.$$.fragment,J),m(a.$$.fragment,J),m(u),m(F),m(b.$$.fragment,J),m(A.$$.fragment,J),m(T.$$.fragment,J),G=!0)},o(J){g(i.$$.fragment,J),g(a.$$.fragment,J),g(u),g(F),g(b.$$.fragment,J),g(A.$$.fragment,J),g(T.$$.fragment,J),G=!1},d(J){J&&v(e),C(i),C(a),~d&&H[d].d(),F&&F.d(),C(b),C(A),C(T),ne=!1,z()}}}function sd(r){let e,t,n;function s(i){r[36](i)}let o={$$slots:{footer:[Yd],header:[Gd]},$$scope:{ctx:r}};return r[14]!==void 0&&(o.collapsed=r[14]),e=new Mo({props:o}),be.push(()=>ke(e,"collapsed",s)),{c(){S(e.$$.fragment)},m(i,l){x(e,i,l),n=!0},p(i,l){const a={};311544&l[0]|131072&l[1]&&(a.$$scope={dirty:l,ctx:i}),!t&&16384&l[0]&&(t=!0,a.collapsed=i[14],Me(()=>t=!1)),e.$set(a)},i(i){n||(m(e.$$.fragment,i),n=!0)},o(i){g(e.$$.fragment,i),n=!1},d(i){C(e,i)}}}function rd(r){let e;return{c(){e=R(r[16])},m(t,n){y(t,e,n)},p(t,n){65536&n[0]&&de(e,t[16])},d(t){t&&v(e)}}}function od(r){var E,I;let e,t,n,s,o,i,l,a,c,d,u=(r[0]==="addRemote"||r[0]==="edit"&&(((E=r[3])==null?void 0:E.type)==="http"||((I=r[3])==null?void 0:I.type)==="sse"))&&Mr(r);function f(A){r[40](A)}let p={size:1,placeholder:"Enter a name for your MCP server (e.g., 'Server Memory')",$$slots:{label:[ud]},$$scope:{ctx:r}};r[8]!==void 0&&(p.value=r[8]),s=new bt({props:p}),be.push(()=>ke(s,"value",f)),s.$on("focus",r[19]);const h=[md,pd],$=[];function b(A,M){var T,O;return A[0]==="addRemote"||((T=A[3])==null?void 0:T.type)==="http"||((O=A[3])==null?void 0:O.type)==="sse"?0:1}return l=b(r),a=$[l]=h[l](r),{c(){u&&u.c(),e=N(),t=k("div"),n=k("div"),S(s.$$.fragment),i=N(),a.c(),c=ve(),w(n,"class","input-field svelte-igdbzh"),w(t,"class","form-row svelte-igdbzh")},m(A,M){u&&u.m(A,M),y(A,e,M),y(A,t,M),_(t,n),x(s,n,null),y(A,i,M),$[l].m(A,M),y(A,c,M),d=!0},p(A,M){var G,ne;A[0]==="addRemote"||A[0]==="edit"&&(((G=A[3])==null?void 0:G.type)==="http"||((ne=A[3])==null?void 0:ne.type)==="sse")?u?(u.p(A,M),9&M[0]&&m(u,1)):(u=Mr(A),u.c(),m(u,1),u.m(e.parentNode,e)):u&&(j(),g(u,1,1,()=>{u=null}),V());const T={};131072&M[1]&&(T.$$scope={dirty:M,ctx:A}),!o&&256&M[0]&&(o=!0,T.value=A[8],Me(()=>o=!1)),s.$set(T);let O=l;l=b(A),l===O?$[l].p(A,M):(j(),g($[O],1,1,()=>{$[O]=null}),V(),a=$[l],a?a.p(A,M):(a=$[l]=h[l](A),a.c()),m(a,1),a.m(c.parentNode,c))},i(A){d||(m(u),m(s.$$.fragment,A),m(a),d=!0)},o(A){g(u),g(s.$$.fragment,A),g(a),d=!1},d(A){A&&(v(e),v(t),v(i),v(c)),u&&u.d(A),C(s),$[l].d(A)}}}function id(r){let e,t,n,s,o,i,l,a,c;function d(f){r[37](f)}n=new Q({props:{size:1,weight:"medium",$$slots:{default:[vd]},$$scope:{ctx:r}}});let u={size:1,placeholder:"Paste JSON here..."};return r[11]!==void 0&&(u.value=r[11]),l=new ko({props:u}),be.push(()=>ke(l,"value",d)),{c(){e=k("div"),t=k("div"),S(n.$$.fragment),s=N(),o=k("div"),i=k("div"),S(l.$$.fragment),w(t,"class","input-field svelte-igdbzh"),w(e,"class","form-row svelte-igdbzh"),w(i,"class","input-field svelte-igdbzh"),w(o,"class","form-row svelte-igdbzh")},m(f,p){y(f,e,p),_(e,t),x(n,t,null),y(f,s,p),y(f,o,p),_(o,i),x(l,i,null),c=!0},p(f,p){const h={};131072&p[1]&&(h.$$scope={dirty:p,ctx:f}),n.$set(h);const $={};!a&&2048&p[0]&&(a=!0,$.value=f[11],Me(()=>a=!1)),l.$set($)},i(f){c||(m(n.$$.fragment,f),m(l.$$.fragment,f),c=!0)},o(f){g(n.$$.fragment,f),g(l.$$.fragment,f),c=!1},d(f){f&&(v(e),v(s),v(o)),C(n),C(l)}}}function Mr(r){let e,t,n,s,o,i,l,a,c;return n=new Q({props:{size:1,weight:"medium",$$slots:{default:[ad]},$$scope:{ctx:r}}}),i=new we({props:{size:1,variant:r[12]==="http"?"solid":"ghost",color:r[12]==="http"?"accent":"neutral",type:"button",$$slots:{default:[ld]},$$scope:{ctx:r}}}),i.$on("click",r[38]),a=new we({props:{size:1,variant:r[12]==="sse"?"solid":"ghost",color:r[12]==="sse"?"accent":"neutral",type:"button",$$slots:{default:[cd]},$$scope:{ctx:r}}}),a.$on("click",r[39]),{c(){e=k("div"),t=k("div"),S(n.$$.fragment),s=N(),o=k("div"),S(i.$$.fragment),l=N(),S(a.$$.fragment),w(o,"class","connection-type-buttons svelte-igdbzh"),w(t,"class","input-field svelte-igdbzh"),w(e,"class","form-row svelte-igdbzh")},m(d,u){y(d,e,u),_(e,t),x(n,t,null),_(t,s),_(t,o),x(i,o,null),_(o,l),x(a,o,null),c=!0},p(d,u){const f={};131072&u[1]&&(f.$$scope={dirty:u,ctx:d}),n.$set(f);const p={};4096&u[0]&&(p.variant=d[12]==="http"?"solid":"ghost"),4096&u[0]&&(p.color=d[12]==="http"?"accent":"neutral"),131072&u[1]&&(p.$$scope={dirty:u,ctx:d}),i.$set(p);const h={};4096&u[0]&&(h.variant=d[12]==="sse"?"solid":"ghost"),4096&u[0]&&(h.color=d[12]==="sse"?"accent":"neutral"),131072&u[1]&&(h.$$scope={dirty:u,ctx:d}),a.$set(h)},i(d){c||(m(n.$$.fragment,d),m(i.$$.fragment,d),m(a.$$.fragment,d),c=!0)},o(d){g(n.$$.fragment,d),g(i.$$.fragment,d),g(a.$$.fragment,d),c=!1},d(d){d&&v(e),C(n),C(i),C(a)}}}function ad(r){let e;return{c(){e=R("Connection Type")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function ld(r){let e;return{c(){e=R("HTTP")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function cd(r){let e;return{c(){e=R("SSE")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function dd(r){let e;return{c(){e=R("Name")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function ud(r){let e,t;return e=new Q({props:{slot:"label",size:1,weight:"medium",$$slots:{default:[dd]},$$scope:{ctx:r}}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,s){const o={};131072&s[1]&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function pd(r){let e,t,n,s,o;function i(a){r[42](a)}let l={size:1,placeholder:"Enter the MCP command (e.g., 'npx -y @modelcontextprotocol/server-memory')",$$slots:{label:[gd]},$$scope:{ctx:r}};return r[9]!==void 0&&(l.value=r[9]),n=new bt({props:l}),be.push(()=>ke(n,"value",i)),n.$on("focus",r[19]),{c(){e=k("div"),t=k("div"),S(n.$$.fragment),w(t,"class","input-field svelte-igdbzh"),w(e,"class","form-row svelte-igdbzh")},m(a,c){y(a,e,c),_(e,t),x(n,t,null),o=!0},p(a,c){const d={};131072&c[1]&&(d.$$scope={dirty:c,ctx:a}),!s&&512&c[0]&&(s=!0,d.value=a[9],Me(()=>s=!1)),n.$set(d)},i(a){o||(m(n.$$.fragment,a),o=!0)},o(a){g(n.$$.fragment,a),o=!1},d(a){a&&v(e),C(n)}}}function md(r){let e,t,n,s,o;function i(a){r[41](a)}let l={size:1,placeholder:"Enter the URL (e.g., 'https://api.example.com/mcp')",$$slots:{label:[hd]},$$scope:{ctx:r}};return r[10]!==void 0&&(l.value=r[10]),n=new bt({props:l}),be.push(()=>ke(n,"value",i)),n.$on("focus",r[19]),{c(){e=k("div"),t=k("div"),S(n.$$.fragment),w(t,"class","input-field svelte-igdbzh"),w(e,"class","form-row svelte-igdbzh")},m(a,c){y(a,e,c),_(e,t),x(n,t,null),o=!0},p(a,c){const d={};131072&c[1]&&(d.$$scope={dirty:c,ctx:a}),!s&&1024&c[0]&&(s=!0,d.value=a[10],Me(()=>s=!1)),n.$set(d)},i(a){o||(m(n.$$.fragment,a),o=!0)},o(a){g(n.$$.fragment,a),o=!1},d(a){a&&v(e),C(n)}}}function fd(r){let e;return{c(){e=R("Command")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function gd(r){let e,t;return e=new Q({props:{slot:"label",size:1,weight:"medium",$$slots:{default:[fd]},$$scope:{ctx:r}}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,s){const o={};131072&s[1]&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function $d(r){let e;return{c(){e=R("URL")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function hd(r){let e,t;return e=new Q({props:{slot:"label",size:1,weight:"medium",$$slots:{default:[$d]},$$scope:{ctx:r}}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,s){const o={};131072&s[1]&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function vd(r){let e;return{c(){e=R("Code Snippet")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Ar(r){let e,t,n;function s(i){r[43](i)}let o={handleEnterEditMode:r[19]};return r[13]!==void 0&&(o.envVarEntries=r[13]),e=new td({props:o}),be.push(()=>ke(e,"envVarEntries",s)),{c(){S(e.$$.fragment)},m(i,l){x(e,i,l),n=!0},p(i,l){const a={};!t&&8192&l[0]&&(t=!0,a.envVarEntries=i[13],Me(()=>t=!1)),e.$set(a)},i(i){n||(m(e.$$.fragment,i),n=!0)},o(i){g(e.$$.fragment,i),n=!1},d(i){C(e,i)}}}function yd(r){let e;return{c(){e=R(r[1])},m(t,n){y(t,e,n)},p(t,n){2&n[0]&&de(e,t[1])},d(t){t&&v(e)}}}function wd(r){let e,t;return e=new di({props:{slot:"icon"}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p:D,i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Sd(r){let e;return{c(){e=R("Cancel")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function xd(r){let e;return{c(){e=R("Save")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Cd(r){let e;return{c(){e=R("Add")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function _d(r){let e;return{c(){e=R("Add")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function bd(r){let e;return{c(){e=R("Import")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function kd(r){let e;function t(o,i){return o[0]==="addJson"?bd:o[0]==="add"?_d:o[0]==="addRemote"?Cd:o[0]==="edit"?xd:void 0}let n=t(r),s=n&&n(r);return{c(){s&&s.c(),e=ve()},m(o,i){s&&s.m(o,i),y(o,e,i)},p(o,i){n!==(n=t(o))&&(s&&s.d(1),s=n&&n(o),s&&(s.c(),s.m(e.parentNode,e)))},d(o){o&&v(e),s&&s.d(o)}}}function Nr(r){let e,t;return e=new Zt({props:{size:1,variant:"ghost",$$slots:{default:[Nd]},$$scope:{ctx:r}}}),e.$on("click",r[35]),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,s){const o={};16384&s[0]|131072&s[1]&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Md(r){let e,t;return e=new rs({}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Ad(r){let e,t;return e=new yi({}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Nd(r){let e,t,n,s;const o=[Ad,Md],i=[];function l(a,c){return a[14]?0:1}return e=l(r),t=i[e]=o[e](r),{c(){t.c(),n=ve()},m(a,c){i[e].m(a,c),y(a,n,c),s=!0},p(a,c){let d=e;e=l(a),e!==d&&(j(),g(i[d],1,1,()=>{i[d]=null}),V(),t=i[e],t||(t=i[e]=o[e](a),t.c()),m(t,1),t.m(n.parentNode,n))},i(a){s||(m(t),s=!0)},o(a){g(t),s=!1},d(a){a&&v(n),i[e].d(a)}}}function Td(r){let e;return{c(){e=k("div"),w(e,"class","c-dot svelte-igdbzh"),he(e,"c-green",!r[6]),he(e,"c-warning",!r[6]&&!!r[7]),he(e,"c-red",!!r[6]),he(e,"c-disabled",r[3].disabled)},m(t,n){y(t,e,n)},p(t,n){64&n[0]&&he(e,"c-green",!t[6]),192&n[0]&&he(e,"c-warning",!t[6]&&!!t[7]),64&n[0]&&he(e,"c-red",!!t[6]),8&n[0]&&he(e,"c-disabled",t[3].disabled)},d(t){t&&v(e)}}}function Tr(r){let e,t,n,s=r[18].length+"";return{c(){e=R("("),t=R(s),n=R(") tools")},m(o,i){y(o,e,i),y(o,t,i),y(o,n,i)},p(o,i){262144&i[0]&&s!==(s=o[18].length+"")&&de(t,s)},d(o){o&&(v(e),v(t),v(n))}}}function Ed(r){let e,t,n,s=r[3].name+"",o=r[18].length>0&&Tr(r);return{c(){e=R(s),t=N(),o&&o.c(),n=ve()},m(i,l){y(i,e,l),y(i,t,l),o&&o.m(i,l),y(i,n,l)},p(i,l){8&l[0]&&s!==(s=i[3].name+"")&&de(e,s),i[18].length>0?o?o.p(i,l):(o=Tr(i),o.c(),o.m(n.parentNode,n)):o&&(o.d(1),o=null)},d(i){i&&(v(e),v(t),v(n)),o&&o.d(i)}}}function Id(r){let e,t,n;return t=new Q({props:{size:1,weight:"medium",$$slots:{default:[Ed]},$$scope:{ctx:r}}}),{c(){e=k("div"),S(t.$$.fragment),w(e,"class","server-name svelte-igdbzh")},m(s,o){y(s,e,o),x(t,e,null),n=!0},p(s,o){const i={};262152&o[0]|131072&o[1]&&(i.$$scope={dirty:o,ctx:s}),t.$set(i)},i(s){n||(m(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&v(e),C(t)}}}function Rd(r){let e,t=bn(r[3])+"";return{c(){e=R(t)},m(n,s){y(n,e,s)},p(n,s){8&s[0]&&t!==(t=bn(n[3])+"")&&de(e,t)},d(n){n&&v(e)}}}function Pd(r){let e,t;return e=new Q({props:{color:"secondary",size:1,weight:"regular",$$slots:{default:[Rd]},$$scope:{ctx:r}}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,s){const o={};8&s[0]|131072&s[1]&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Ld(r){let e,t,n,s,o,i,l,a,c,d=r[18].length>0&&Nr(r);return n=new rt({props:{content:r[6]||r[7],$$slots:{default:[Td]},$$scope:{ctx:r}}}),o=new rt({props:{content:r[3].name,side:"top",align:"start",$$slots:{default:[Id]},$$scope:{ctx:r}}}),a=new rt({props:{content:bn(r[3]),side:"top",align:"start",$$slots:{default:[Pd]},$$scope:{ctx:r}}}),{c(){e=k("div"),d&&d.c(),t=N(),S(n.$$.fragment),s=N(),S(o.$$.fragment),i=N(),l=k("div"),S(a.$$.fragment),w(l,"class","command-text svelte-igdbzh"),w(e,"slot","header-left"),w(e,"class","l-header svelte-igdbzh")},m(u,f){y(u,e,f),d&&d.m(e,null),_(e,t),x(n,e,null),_(e,s),x(o,e,null),_(e,i),_(e,l),x(a,l,null),c=!0},p(u,f){u[18].length>0?d?(d.p(u,f),262144&f[0]&&m(d,1)):(d=Nr(u),d.c(),m(d,1),d.m(e,t)):d&&(j(),g(d,1,1,()=>{d=null}),V());const p={};192&f[0]&&(p.content=u[6]||u[7]),200&f[0]|131072&f[1]&&(p.$$scope={dirty:f,ctx:u}),n.$set(p);const h={};8&f[0]&&(h.content=u[3].name),262152&f[0]|131072&f[1]&&(h.$$scope={dirty:f,ctx:u}),o.$set(h);const $={};8&f[0]&&($.content=bn(u[3])),8&f[0]|131072&f[1]&&($.$$scope={dirty:f,ctx:u}),a.$set($)},i(u){c||(m(d),m(n.$$.fragment,u),m(o.$$.fragment,u),m(a.$$.fragment,u),c=!0)},o(u){g(d),g(n.$$.fragment,u),g(o.$$.fragment,u),g(a.$$.fragment,u),c=!1},d(u){u&&v(e),d&&d.d(),C(n),C(o),C(a)}}}function Od(r){let e,t;return e=new bi({}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function zd(r){let e,t;return e=new Zt({props:{size:1,variant:"ghost-block",color:"neutral",$$slots:{default:[Od]},$$scope:{ctx:r}}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,s){const o={};131072&s[1]&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Fd(r){let e;return{c(){e=R("Edit")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Zd(r){let e,t,n,s,o;return t=new ki({}),s=new Q({props:{size:1,weight:"medium",$$slots:{default:[Fd]},$$scope:{ctx:r}}}),{c(){e=k("div"),S(t.$$.fragment),n=N(),S(s.$$.fragment),w(e,"class","status-controls-button svelte-igdbzh")},m(i,l){y(i,e,l),x(t,e,null),_(e,n),x(s,e,null),o=!0},p(i,l){const a={};131072&l[1]&&(a.$$scope={dirty:l,ctx:i}),s.$set(a)},i(i){o||(m(t.$$.fragment,i),m(s.$$.fragment,i),o=!0)},o(i){g(t.$$.fragment,i),g(s.$$.fragment,i),o=!1},d(i){i&&v(e),C(t),C(s)}}}function Ud(r){let e;return{c(){e=R("Copy JSON")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Dd(r){let e,t,n,s,o;return t=new Mi({}),s=new Q({props:{size:1,weight:"medium",$$slots:{default:[Ud]},$$scope:{ctx:r}}}),{c(){e=k("div"),S(t.$$.fragment),n=N(),S(s.$$.fragment),w(e,"class","status-controls-button svelte-igdbzh")},m(i,l){y(i,e,l),x(t,e,null),_(e,n),x(s,e,null),o=!0},p(i,l){const a={};131072&l[1]&&(a.$$scope={dirty:l,ctx:i}),s.$set(a)},i(i){o||(m(t.$$.fragment,i),m(s.$$.fragment,i),o=!0)},o(i){g(t.$$.fragment,i),g(s.$$.fragment,i),o=!1},d(i){i&&v(e),C(t),C(s)}}}function jd(r){let e;return{c(){e=R("Delete")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Vd(r){let e,t,n,s,o;return t=new _o({}),s=new Q({props:{size:1,weight:"medium",$$slots:{default:[jd]},$$scope:{ctx:r}}}),{c(){e=k("div"),S(t.$$.fragment),n=N(),S(s.$$.fragment),w(e,"class","status-controls-button svelte-igdbzh")},m(i,l){y(i,e,l),x(t,e,null),_(e,n),x(s,e,null),o=!0},p(i,l){const a={};131072&l[1]&&(a.$$scope={dirty:l,ctx:i}),s.$set(a)},i(i){o||(m(t.$$.fragment,i),m(s.$$.fragment,i),o=!0)},o(i){g(t.$$.fragment,i),g(s.$$.fragment,i),o=!1},d(i){i&&v(e),C(t),C(s)}}}function qd(r){let e,t,n,s,o,i;return e=new xe.Item({props:{onSelect:r[19],$$slots:{default:[Zd]},$$scope:{ctx:r}}}),n=new xe.Item({props:{onSelect:r[32],$$slots:{default:[Dd]},$$scope:{ctx:r}}}),o=new xe.Item({props:{color:"error",onSelect:r[33],$$slots:{default:[Vd]},$$scope:{ctx:r}}}),{c(){S(e.$$.fragment),t=N(),S(n.$$.fragment),s=N(),S(o.$$.fragment)},m(l,a){x(e,l,a),y(l,t,a),x(n,l,a),y(l,s,a),x(o,l,a),i=!0},p(l,a){const c={};131072&a[1]&&(c.$$scope={dirty:a,ctx:l}),e.$set(c);const d={};32768&a[0]&&(d.onSelect=l[32]),131072&a[1]&&(d.$$scope={dirty:a,ctx:l}),n.$set(d);const u={};32792&a[0]&&(u.onSelect=l[33]),131072&a[1]&&(u.$$scope={dirty:a,ctx:l}),o.$set(u)},i(l){i||(m(e.$$.fragment,l),m(n.$$.fragment,l),m(o.$$.fragment,l),i=!0)},o(l){g(e.$$.fragment,l),g(n.$$.fragment,l),g(o.$$.fragment,l),i=!1},d(l){l&&(v(t),v(s)),C(e,l),C(n,l),C(o,l)}}}function Hd(r){let e,t,n,s;return e=new xe.Trigger({props:{$$slots:{default:[zd]},$$scope:{ctx:r}}}),n=new xe.Content({props:{side:"bottom",align:"end",$$slots:{default:[qd]},$$scope:{ctx:r}}}),{c(){S(e.$$.fragment),t=N(),S(n.$$.fragment)},m(o,i){x(e,o,i),y(o,t,i),x(n,o,i),s=!0},p(o,i){const l={};131072&i[1]&&(l.$$scope={dirty:i,ctx:o}),e.$set(l);const a={};32792&i[0]|131072&i[1]&&(a.$$scope={dirty:i,ctx:o}),n.$set(a)},i(o){s||(m(e.$$.fragment,o),m(n.$$.fragment,o),s=!0)},o(o){g(e.$$.fragment,o),g(n.$$.fragment,o),s=!1},d(o){o&&v(t),C(e,o),C(n,o)}}}function Bd(r){let e,t,n,s,o,i,l=ri(),a=l&&function(u){let f,p;return f=new ts({props:{size:1,checked:!u[3].disabled}}),f.$on("change",u[31]),{c(){S(f.$$.fragment)},m(h,$){x(f,h,$),p=!0},p(h,$){const b={};8&$[0]&&(b.checked=!h[3].disabled),f.$set(b)},i(h){p||(m(f.$$.fragment,h),p=!0)},o(h){g(f.$$.fragment,h),p=!1},d(h){C(f,h)}}}(r);function c(u){r[34](u)}let d={$$slots:{default:[Hd]},$$scope:{ctx:r}};return r[15]!==void 0&&(d.requestClose=r[15]),s=new xe.Root({props:d}),be.push(()=>ke(s,"requestClose",c)),{c(){e=k("div"),t=k("div"),a&&a.c(),n=N(),S(s.$$.fragment),w(t,"class","status-controls svelte-igdbzh"),w(e,"class","server-actions svelte-igdbzh"),w(e,"slot","header-right")},m(u,f){y(u,e,f),_(e,t),a&&a.m(t,null),_(t,n),x(s,t,null),i=!0},p(u,f){l&&a.p(u,f);const p={};32792&f[0]|131072&f[1]&&(p.$$scope={dirty:f,ctx:u}),!o&&32768&f[0]&&(o=!0,p.requestClose=u[15],Me(()=>o=!1)),s.$set(p)},i(u){i||(m(a),m(s.$$.fragment,u),i=!0)},o(u){g(a),g(s.$$.fragment,u),i=!1},d(u){u&&v(e),a&&a.d(),C(s)}}}function Gd(r){let e,t;return e=new wt({props:{slot:"header",$$slots:{"header-right":[Bd],"header-left":[Ld]},$$scope:{ctx:r}}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,s){const o={};311544&s[0]|131072&s[1]&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Jd(r){let e,t=(r[45].definition.mcp_tool_name||r[45].definition.name)+"";return{c(){e=R(t)},m(n,s){y(n,e,s)},p(n,s){262144&s[0]&&t!==(t=(n[45].definition.mcp_tool_name||n[45].definition.name)+"")&&de(e,t)},d(n){n&&v(e)}}}function Er(r){let e,t;return e=new Q({props:{size:1,color:"secondary",$$slots:{default:[Wd]},$$scope:{ctx:r}}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,s){const o={};262144&s[0]|131072&s[1]&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Wd(r){let e,t=r[45].definition.description+"";return{c(){e=R(t)},m(n,s){y(n,e,s)},p(n,s){262144&s[0]&&t!==(t=n[45].definition.description+"")&&de(e,t)},d(n){n&&v(e)}}}function Kd(r){let e,t,n=r[45].definition.description&&Er(r);return{c(){n&&n.c(),e=ve()},m(s,o){n&&n.m(s,o),y(s,e,o),t=!0},p(s,o){s[45].definition.description?n?(n.p(s,o),262144&o[0]&&m(n,1)):(n=Er(s),n.c(),m(n,1),n.m(e.parentNode,e)):n&&(j(),g(n,1,1,()=>{n=null}),V())},i(s){t||(m(n),t=!0)},o(s){g(n),t=!1},d(s){s&&v(e),n&&n.d(s)}}}function Ir(r){let e,t,n,s,o,i,l,a,c,d,u;return i=new Q({props:{size:1,weight:"medium",$$slots:{default:[Jd]},$$scope:{ctx:r}}}),c=new rt({props:{content:r[45].definition.description,align:"start",$$slots:{default:[Kd]},$$scope:{ctx:r}}}),{c(){e=k("div"),t=k("div"),n=k("div"),s=k("div"),o=N(),S(i.$$.fragment),l=N(),a=k("div"),S(c.$$.fragment),d=N(),w(s,"class","tool-status-dot svelte-igdbzh"),he(s,"enabled",r[45].enabled),he(s,"disabled",!r[45].enabled),w(n,"class","tool-status svelte-igdbzh"),w(a,"class","c-tool-description svelte-igdbzh"),w(t,"class","c-tool-info svelte-igdbzh"),w(e,"class","c-tool-item svelte-igdbzh")},m(f,p){y(f,e,p),_(e,t),_(t,n),_(n,s),_(n,o),x(i,n,null),_(t,l),_(t,a),x(c,a,null),_(e,d),u=!0},p(f,p){(!u||262144&p[0])&&he(s,"enabled",f[45].enabled),(!u||262144&p[0])&&he(s,"disabled",!f[45].enabled);const h={};262144&p[0]|131072&p[1]&&(h.$$scope={dirty:p,ctx:f}),i.$set(h);const $={};262144&p[0]&&($.content=f[45].definition.description),262144&p[0]|131072&p[1]&&($.$$scope={dirty:p,ctx:f}),c.$set($)},i(f){u||(m(i.$$.fragment,f),m(c.$$.fragment,f),u=!0)},o(f){g(i.$$.fragment,f),g(c.$$.fragment,f),u=!1},d(f){f&&v(e),C(i),C(c)}}}function Yd(r){let e,t,n=ce(r[18]),s=[];for(let i=0;i<n.length;i+=1)s[i]=Ir(kr(r,n,i));const o=i=>g(s[i],1,1,()=>{s[i]=null});return{c(){e=k("div");for(let i=0;i<s.length;i+=1)s[i].c();w(e,"slot","footer")},m(i,l){y(i,e,l);for(let a=0;a<s.length;a+=1)s[a]&&s[a].m(e,null);t=!0},p(i,l){if(262144&l[0]){let a;for(n=ce(i[18]),a=0;a<n.length;a+=1){const c=kr(i,n,a);s[a]?(s[a].p(c,l),m(s[a],1)):(s[a]=Ir(c),s[a].c(),m(s[a],1),s[a].m(e,null))}for(j(),a=n.length;a<s.length;a+=1)o(a);V()}},i(i){if(!t){for(let l=0;l<n.length;l+=1)m(s[l]);t=!0}},o(i){s=s.filter(Boolean);for(let l=0;l<s.length;l+=1)g(s[l]);t=!1},d(i){i&&v(e),yt(s,i)}}}function Xd(r){let e,t,n,s;const o=[sd,nd],i=[];function l(a,c){return a[0]==="view"&&a[3]?0:1}return e=l(r),t=i[e]=o[e](r),{c(){t.c(),n=ve()},m(a,c){i[e].m(a,c),y(a,n,c),s=!0},p(a,c){let d=e;e=l(a),e===d?i[e].p(a,c):(j(),g(i[d],1,1,()=>{i[d]=null}),V(),t=i[e],t?t.p(a,c):(t=i[e]=o[e](a),t.c()),m(t,1),t.m(n.parentNode,n))},i(a){s||(m(t),s=!0)},o(a){g(t),s=!1},d(a){a&&v(n),i[e].d(a)}}}function Qd({key:r,value:e}){return r.trim()&&e.trim()}function eu(r,e,t){let n,s,o,i,l,{server:a=null}=e,{onDelete:c}=e,{onAdd:d}=e,{onSave:u}=e,{onEdit:f}=e,{onToggleDisableServer:p}=e,{onJSONImport:h}=e,{onCancel:$}=e,{disabledText:b}=e,{warningText:E}=e,{mode:I="view"}=e,{mcpServerError:A=""}=e,M=(a==null?void 0:a.name)??"",T=je(a)?"":It(a)?a.command:"",O=je(a)?a.url:"",G=It(a)?a.env??{}:{},ne="",z=je(a)?a.type:"http",Z=[];H();let P=!0;function H(){t(13,Z=Object.entries(G).map(([U,fe])=>({id:crypto.randomUUID(),key:U,value:fe})))}let ie=()=>{},{busy:F=!1}=e;function J(){if(a){const U=Ln.convertServerToJSON(a);navigator.clipboard.writeText(U)}}return r.$$set=U=>{"server"in U&&t(3,a=U.server),"onDelete"in U&&t(4,c=U.onDelete),"onAdd"in U&&t(23,d=U.onAdd),"onSave"in U&&t(24,u=U.onSave),"onEdit"in U&&t(25,f=U.onEdit),"onToggleDisableServer"in U&&t(5,p=U.onToggleDisableServer),"onJSONImport"in U&&t(26,h=U.onJSONImport),"onCancel"in U&&t(27,$=U.onCancel),"disabledText"in U&&t(6,b=U.disabledText),"warningText"in U&&t(7,E=U.warningText),"mode"in U&&t(0,I=U.mode),"mcpServerError"in U&&t(1,A=U.mcpServerError),"busy"in U&&t(2,F=U.busy)},r.$$.update=()=>{8&r.$$.dirty[0]&&t(18,n=(a==null?void 0:a.tools)??[]),768&r.$$.dirty[0]&&M&&T&&t(1,A=""),1793&r.$$.dirty[0]&&t(30,s=!((I!=="add"||M.trim()&&T.trim())&&(I!=="addRemote"||M.trim()&&O.trim()))),2049&r.$$.dirty[0]&&t(29,o=I==="addJson"&&!ne.trim()),1610612737&r.$$.dirty[0]&&t(17,i=s||I==="view"||o),1&r.$$.dirty[0]&&t(16,l=(()=>{switch(I){case"add":return"New MCP Server";case"addRemote":return"New Remote MCP Server";case"addJson":return"Import MCP Server";default:return"Edit MCP Server"}})())},[I,A,F,a,c,p,b,E,M,T,O,ne,z,Z,P,ie,l,i,n,function(){a&&I==="view"&&(t(0,I="edit"),f(a),ie())},J,async function(){t(1,A=""),t(2,F=!0);const U=Z.filter(Qd);G=Object.fromEntries(U.map(({key:fe,value:De})=>[fe.trim(),De.trim()])),H();try{if(I==="add"){const fe={type:"stdio",name:M.trim(),command:T.trim(),arguments:"",useShellInterpolation:!0,env:Object.keys(G).length>0?G:void 0};await d(fe)}else if(I==="addRemote"){const fe={type:z,name:M.trim(),url:O.trim()};await d(fe)}else if(I==="addJson"){try{JSON.parse(ne)}catch(fe){const De=fe instanceof Error?fe.message:String(fe);throw new Ce(`Invalid JSON format: ${De}`)}await h(ne)}else if(I==="edit"&&a){if(je(a)){const fe={...a,type:z,name:M.trim(),url:O.trim()};await u(fe)}else if(It(a)){const fe={...a,name:M.trim(),command:T.trim(),arguments:"",env:Object.keys(G).length>0?G:void 0};await u(fe)}}}catch(fe){t(1,A=fe instanceof Ce?fe.message:"Failed to save server"),console.warn(fe)}finally{t(2,F=!1)}},function(){t(2,F=!1),t(1,A=""),$==null||$(),t(11,ne=""),t(8,M=(a==null?void 0:a.name)??""),t(9,T=je(a)?"":It(a)?a.command:""),t(10,O=je(a)?a.url:""),G=It(a)&&a.env?{...a.env}:{},t(12,z=je(a)?a.type:"http"),H()},d,u,f,h,$,H,o,s,()=>{a&&p(a.id),ie()},()=>{J(),ie()},()=>{c(a.id),ie()},function(U){ie=U,t(15,ie)},()=>t(14,P=!P),function(U){P=U,t(14,P)},function(U){ne=U,t(11,ne)},()=>t(12,z="http"),()=>t(12,z="sse"),function(U){M=U,t(8,M)},function(U){O=U,t(10,O)},function(U){T=U,t(9,T)},function(U){Z=U,t(13,Z)}]}class Go extends ae{constructor(e){super(),le(this,e,eu,Xd,oe,{server:3,onDelete:4,onAdd:23,onSave:24,onEdit:25,onToggleDisableServer:5,onJSONImport:26,onCancel:27,disabledText:6,warningText:7,mode:0,mcpServerError:1,setLocalEnvVarFormState:28,busy:2},null,[-1,-1])}get setLocalEnvVarFormState(){return this.$$.ctx[28]}}function Rr(r,e,t){const n=r.slice();return n[18]=e[t],n}function Pr(r,e,t){const n=r.slice();n[21]=e[t],n[24]=t;const s=n[21].type==="environmentVariable"&&n[21].envVarName?n[21].envVarName:n[21].correspondingArg||`input_${n[24]}`;return n[22]=s,n}function tu(r){let e,t=r[18].label+"";return{c(){e=R(t)},m(n,s){y(n,e,s)},p:D,d(n){n&&v(e)}}}function nu(r){let e,t=r[18].description+"";return{c(){e=R(t)},m(n,s){y(n,e,s)},p:D,d(n){n&&v(e)}}}function Lr(r){let e,t,n,s,o,i,l,a=ce(r[18].userInput),c=[];for(let u=0;u<a.length;u+=1)c[u]=Or(Pr(r,a,u));const d=u=>g(c[u],1,1,()=>{c[u]=null});return s=new we({props:{variant:"ghost-block",color:"accent",size:1,$$slots:{default:[ou]},$$scope:{ctx:r}}}),s.$on("click",function(){return r[16](r[18])}),i=new we({props:{variant:"ghost-block",color:"neutral",size:1,$$slots:{default:[iu]},$$scope:{ctx:r}}}),i.$on("click",r[8]),{c(){e=k("div");for(let u=0;u<c.length;u+=1)c[u].c();t=N(),n=k("div"),S(s.$$.fragment),o=N(),S(i.$$.fragment),w(n,"class","user-input-actions svelte-8tbe79"),w(e,"class","user-input-container svelte-8tbe79")},m(u,f){y(u,e,f);for(let p=0;p<c.length;p+=1)c[p]&&c[p].m(e,null);_(e,t),_(e,n),x(s,n,null),_(n,o),x(i,n,null),l=!0},p(u,f){if(r=u,440&f){let $;for(a=ce(r[18].userInput),$=0;$<a.length;$+=1){const b=Pr(r,a,$);c[$]?(c[$].p(b,f),m(c[$],1)):(c[$]=Or(b),c[$].c(),m(c[$],1),c[$].m(e,t))}for(j(),$=a.length;$<c.length;$+=1)d($);V()}const p={};33554432&f&&(p.$$scope={dirty:f,ctx:r}),s.$set(p);const h={};33554432&f&&(h.$$scope={dirty:f,ctx:r}),i.$set(h)},i(u){if(!l){for(let f=0;f<a.length;f+=1)m(c[f]);m(s.$$.fragment,u),m(i.$$.fragment,u),l=!0}},o(u){c=c.filter(Boolean);for(let f=0;f<c.length;f+=1)g(c[f]);g(s.$$.fragment,u),g(i.$$.fragment,u),l=!1},d(u){u&&v(e),yt(c,u),C(s),C(i)}}}function su(r){let e,t=r[21].label+"";return{c(){e=R(t)},m(n,s){y(n,e,s)},p:D,d(n){n&&v(e)}}}function ru(r){let e,t=r[21].description+"";return{c(){e=R(t)},m(n,s){y(n,e,s)},p:D,d(n){n&&v(e)}}}function Or(r){let e,t,n,s,o,i,l,a;t=new Q({props:{size:1,weight:"medium",color:"neutral",$$slots:{default:[su]},$$scope:{ctx:r}}});let c=r[21].description&&function(p){let h,$;return h=new Q({props:{size:1,color:"secondary",$$slots:{default:[ru]},$$scope:{ctx:p}}}),{c(){S(h.$$.fragment)},m(b,E){x(h,b,E),$=!0},p(b,E){const I={};33554432&E&&(I.$$scope={dirty:E,ctx:b}),h.$set(I)},i(b){$||(m(h.$$.fragment,b),$=!0)},o(b){g(h.$$.fragment,b),$=!1},d(b){C(h,b)}}}(r);function d(p){r[13](p,r[22])}function u(p){r[14](p,r[22])}let f={placeholder:r[21].placeholder||"",size:1,variant:"surface"};return r[3][r[22]]!==void 0&&(f.value=r[3][r[22]]),r[4][r[22]]!==void 0&&(f.textInput=r[4][r[22]]),o=new bt({props:f}),be.push(()=>ke(o,"value",d)),be.push(()=>ke(o,"textInput",u)),o.$on("keydown",function(...p){return r[15](r[18],...p)}),{c(){e=k("div"),S(t.$$.fragment),n=N(),c&&c.c(),s=N(),S(o.$$.fragment),w(e,"class","user-input-field svelte-8tbe79")},m(p,h){y(p,e,h),x(t,e,null),_(e,n),c&&c.m(e,null),_(e,s),x(o,e,null),a=!0},p(p,h){r=p;const $={};33554432&h&&($.$$scope={dirty:h,ctx:r}),t.$set($),r[21].description&&c.p(r,h);const b={};!i&&40&h&&(i=!0,b.value=r[3][r[22]],Me(()=>i=!1)),!l&&48&h&&(l=!0,b.textInput=r[4][r[22]],Me(()=>l=!1)),o.$set(b)},i(p){a||(m(t.$$.fragment,p),m(c),m(o.$$.fragment,p),a=!0)},o(p){g(t.$$.fragment,p),g(c),g(o.$$.fragment,p),a=!1},d(p){p&&v(e),C(t),c&&c.d(),C(o)}}}function ou(r){let e;return{c(){e=R("Install")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function iu(r){let e;return{c(){e=R("Cancel")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function au(r){let e,t,n,s,o,i;n=new Q({props:{size:1,weight:"medium",$$slots:{default:[tu]},$$scope:{ctx:r}}});let l=r[18].description&&function(c){let d,u;return d=new Q({props:{size:1,color:"secondary",$$slots:{default:[nu]},$$scope:{ctx:c}}}),{c(){S(d.$$.fragment)},m(f,p){x(d,f,p),u=!0},p(f,p){const h={};33554432&p&&(h.$$scope={dirty:p,ctx:f}),d.$set(h)},i(f){u||(m(d.$$.fragment,f),u=!0)},o(f){g(d.$$.fragment,f),u=!1},d(f){C(d,f)}}}(r),a=r[2]===r[18].value&&r[18].userInput&&Lr(r);return{c(){e=k("div"),t=k("div"),S(n.$$.fragment),s=N(),l&&l.c(),o=N(),a&&a.c(),w(t,"class","mcp-service-title svelte-8tbe79"),w(e,"slot","header-left"),w(e,"class","mcp-service-info svelte-8tbe79")},m(c,d){y(c,e,d),_(e,t),x(n,t,null),_(e,s),l&&l.m(e,null),_(e,o),a&&a.m(e,null),i=!0},p(c,d){const u={};33554432&d&&(u.$$scope={dirty:d,ctx:c}),n.$set(u),c[18].description&&l.p(c,d),c[2]===c[18].value&&c[18].userInput?a?(a.p(c,d),4&d&&m(a,1)):(a=Lr(c),a.c(),m(a,1),a.m(e,null)):a&&(j(),g(a,1,1,()=>{a=null}),V())},i(c){i||(m(n.$$.fragment,c),m(l),m(a),i=!0)},o(c){g(n.$$.fragment,c),g(l),g(a),i=!1},d(c){c&&v(e),C(n),l&&l.d(),a&&a.d()}}}function lu(r){let e,t;return e=new we({props:{variant:"ghost-block",color:"accent",size:1,$$slots:{default:[du]},$$scope:{ctx:r}}}),e.$on("click",function(){return r[12](r[18])}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,s){r=n;const o={};33554432&s&&(o.$$scope={dirty:s,ctx:r}),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function cu(r){let e,t,n;return t=new es.Root({props:{color:"success",size:1,variant:"soft",$$slots:{default:[uu]},$$scope:{ctx:r}}}),{c(){e=k("div"),S(t.$$.fragment),w(e,"class","installed-indicator svelte-8tbe79")},m(s,o){y(s,e,o),x(t,e,null),n=!0},p(s,o){const i={};33554432&o&&(i.$$scope={dirty:o,ctx:s}),t.$set(i)},i(s){n||(m(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&v(e),C(t)}}}function du(r){let e;return{c(){e=k("span"),e.textContent="+"},m(t,n){y(t,e,n)},p:D,d(t){t&&v(e)}}}function uu(r){let e;return{c(){e=R("Installed")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function pu(r){let e,t,n,s,o;function i(...d){return r[11](r[18],...d)}const l=[cu,lu],a=[];function c(d,u){return 1&u&&(t=null),t==null&&(t=!!d[0].some(i)),t?0:1}return n=c(r,-1),s=a[n]=l[n](r),{c(){e=k("div"),s.c(),w(e,"slot","header-right"),w(e,"class","mcp-service-actions svelte-8tbe79")},m(d,u){y(d,e,u),a[n].m(e,null),o=!0},p(d,u){let f=n;n=c(r=d,u),n===f?a[n].p(r,u):(j(),g(a[f],1,1,()=>{a[f]=null}),V(),s=a[n],s?s.p(r,u):(s=a[n]=l[n](r),s.c()),m(s,1),s.m(e,null))},i(d){o||(m(s),o=!0)},o(d){g(s),o=!1},d(d){d&&v(e),a[n].d()}}}function zr(r){let e,t,n,s;return t=new wt({props:{$$slots:{"header-right":[pu],"header-left":[au]},$$scope:{ctx:r}}}),{c(){e=k("div"),S(t.$$.fragment),n=N(),w(e,"class","mcp-service-item")},m(o,i){y(o,e,i),x(t,e,null),_(e,n),s=!0},p(o,i){const l={};33554461&i&&(l.$$scope={dirty:i,ctx:o}),t.$set(l)},i(o){s||(m(t.$$.fragment,o),s=!0)},o(o){g(t.$$.fragment,o),s=!1},d(o){o&&v(e),C(t)}}}function mu(r){let e,t,n,s=ce(r[5]),o=[];for(let l=0;l<s.length;l+=1)o[l]=zr(Rr(r,s,l));const i=l=>g(o[l],1,1,()=>{o[l]=null});return{c(){e=k("div"),t=k("div");for(let l=0;l<o.length;l+=1)o[l].c();w(t,"class","mcp-list-container svelte-8tbe79"),w(e,"class","mcp-install-content svelte-8tbe79")},m(l,a){y(l,e,a),_(e,t);for(let c=0;c<o.length;c+=1)o[c]&&o[c].m(t,null);n=!0},p(l,a){if(509&a){let c;for(s=ce(l[5]),c=0;c<s.length;c+=1){const d=Rr(l,s,c);o[c]?(o[c].p(d,a),m(o[c],1)):(o[c]=zr(d),o[c].c(),m(o[c],1),o[c].m(t,null))}for(j(),c=s.length;c<o.length;c+=1)i(c);V()}},i(l){if(!n){for(let a=0;a<s.length;a+=1)m(o[a]);n=!0}},o(l){o=o.filter(Boolean);for(let a=0;a<o.length;a+=1)g(o[a]);n=!1},d(l){l&&v(e),yt(o,l)}}}function fu(r){let e;return{c(){e=R("Easy MCP Installation")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function gu(r){let e,t,n,s,o;return t=new Ni({}),s=new Q({props:{color:"neutral",size:1,weight:"light",class:"card-title",$$slots:{default:[fu]},$$scope:{ctx:r}}}),{c(){e=k("div"),S(t.$$.fragment),n=N(),S(s.$$.fragment),w(e,"slot","header-left"),w(e,"class","mcp-install-left svelte-8tbe79")},m(i,l){y(i,e,l),x(t,e,null),_(e,n),x(s,e,null),o=!0},p(i,l){const a={};33554432&l&&(a.$$scope={dirty:l,ctx:i}),s.$set(a)},i(i){o||(m(t.$$.fragment,i),m(s.$$.fragment,i),o=!0)},o(i){g(t.$$.fragment,i),g(s.$$.fragment,i),o=!1},d(i){i&&v(e),C(t),C(s)}}}function $u(r){let e,t,n;return t=new wt({props:{$$slots:{"header-left":[gu]},$$scope:{ctx:r}}}),{c(){e=k("div"),S(t.$$.fragment),w(e,"slot","header"),w(e,"class","mcp-install-header svelte-8tbe79")},m(s,o){y(s,e,o),x(t,e,null),n=!0},p(s,o){const i={};33554432&o&&(i.$$scope={dirty:o,ctx:s}),t.$set(i)},i(s){n||(m(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&v(e),C(t)}}}function hu(r){let e,t,n,s;function o(l){r[17](l)}let i={$$slots:{header:[$u],default:[mu]},$$scope:{ctx:r}};return r[1]!==void 0&&(i.collapsed=r[1]),t=new Mo({props:i}),be.push(()=>ke(t,"collapsed",o)),{c(){e=k("div"),S(t.$$.fragment),w(e,"class","mcp-install-wrapper svelte-8tbe79")},m(l,a){y(l,e,a),x(t,e,null),s=!0},p(l,[a]){const c={};33554461&a&&(c.$$scope={dirty:a,ctx:l}),!n&&2&a&&(n=!0,c.collapsed=l[1],Me(()=>n=!1)),t.$set(c)},i(l){s||(m(t.$$.fragment,l),s=!0)},o(l){g(t.$$.fragment,l),s=!1},d(l){l&&v(e),C(t)}}}const Vn="easyMCPInstall.collapsed";function vu(r,e,t){let{onMCPServerAdd:n}=e,{servers:s=[]}=e,o=!1,i=!1,l=null,a={},c={};function d(p){if(s.some($=>$.name===p.label))return;if(p.userInput&&p.userInput.length>0)return t(3,a={}),p.userInput.forEach(($,b)=>{let E;E=$.type==="environmentVariable"&&$.envVarName?$.envVarName:$.correspondingArg?$.correspondingArg:`input_${b}`,t(3,a[E]=$.defaultValue||"",a)}),void t(2,l=p.value);const h={type:"stdio",name:p.label,command:p.command,arguments:"",useShellInterpolation:!0};n&&n(h)}function u(p){var E;if(!p.userInput)return;for(let I=0;I<p.userInput.length;I++){const A=p.userInput[I];let M;if(M=A.type==="environmentVariable"&&A.envVarName?A.envVarName:A.correspondingArg?A.correspondingArg:`input_${I}`,!((E=a[M])==null?void 0:E.trim())){const O=c[M];return void(O&&O.focus())}}let h=[p.command],$={};p.args&&h.push(...p.args);for(let I=0;I<p.userInput.length;I++){const A=p.userInput[I];let M;M=A.type==="environmentVariable"&&A.envVarName?A.envVarName:A.correspondingArg?A.correspondingArg:`input_${I}`;const T=a[M].trim(),O=`"${T}"`;if(A.type==="environmentVariable"&&A.envVarName)$[A.envVarName]=T;else if(A.correspondingArg){const G=h.indexOf(A.correspondingArg);G!==-1?h.splice(G+1,0,O):h.push(A.correspondingArg,O)}else h.push(O)}const b={type:"stdio",name:p.label,command:h.join(" "),arguments:"",useShellInterpolation:!0,env:Object.keys($).length>0?$:void 0};n&&n(b),t(2,l=null),t(3,a={})}function f(){t(2,l=null),t(3,a={})}r.$$set=p=>{"onMCPServerAdd"in p&&t(9,n=p.onMCPServerAdd),"servers"in p&&t(0,s=p.servers)},r.$$.update=()=>{1026&r.$$.dirty&&typeof window<"u"&&i&&localStorage.setItem(Vn,JSON.stringify(o))};{const p=localStorage.getItem(Vn);if(p!==null)try{t(1,o=JSON.parse(p))}catch{localStorage.removeItem(Vn)}t(10,i=!0)}return[s,o,l,a,c,[{value:"redis",label:"Redis",description:"In-memory data structure store",command:"uvx",args:["--from","git+https://github.com/redis/mcp-redis.git","redis-mcp-server","--url"],userInput:[{label:"Redis connection URL",description:"Enter your connection URL (redis://localhost:6379/0)",placeholder:"rediss://<USERNAME>:<PASSWORD>@<HOST>:<PORT>?ssl_cert_reqs=required&ssl_ca_certs=<PATH_TO_CERT>",correspondingArg:"--url",type:"argument"}]},{value:"mongodb",label:"MongoDB",description:"Optimize database queries and performance.",command:"npx",args:["-y","mongodb-mcp-server","--connectionString"],userInput:[{label:"MongoDB Connection String",description:"Enter your MongoDB connection string",placeholder:"********************************:port/database",correspondingArg:"--connectionString",type:"argument"}]},{value:"circleci",label:"CircleCI",description:"Debug builds and improve CI/CD pipelines.",command:"npx",args:["-y","@circleci/mcp-server-circleci"],userInput:[{label:"CircleCI Token",description:"Enter your CircleCI token",placeholder:"YOUR_CIRCLE_CI_TOKEN",type:"environmentVariable",envVarName:"CIRCLECI_TOKEN"},{label:"Base URL",description:"Enter the base URL for your CircleCI instance",placeholder:"https://circleci.com",defaultValue:"https://circleci.com",type:"environmentVariable",envVarName:"CIRCLECI_BASE_URL"}]},{value:"context7",label:"Context 7",description:"Package documentation",command:"npx -y @upstash/context7-mcp@latest"},{value:"playwright",label:"Playwright",description:"Browser automation",command:"npx -y @playwright/mcp@latest"},{value:"sequential-thinking",label:"Sequential thinking",description:"Think through complex problems step-by-step.",command:"npx -y @modelcontextprotocol/server-sequential-thinking"}],d,u,f,n,i,(p,h)=>h.name===p.label,p=>d(p),function(p,h){r.$$.not_equal(a[h],p)&&(a[h]=p,t(3,a))},function(p,h){r.$$.not_equal(c[h],p)&&(c[h]=p,t(4,c))},(p,h)=>{h.key==="Enter"?u(p):h.key==="Escape"&&f()},p=>u(p),function(p){o=p,t(1,o)}]}class yu extends ae{constructor(e){super(),le(this,e,vu,hu,oe,{onMCPServerAdd:9,servers:0})}}const wu={mcpDocsURL:"https://docs.augmentcode.com/setup-augment/mcp"},Su={mcpDocsURL:"https://docs.augmentcode.com/jetbrains/setup-augment/mcp"},xu=oi(),Cu=new class{constructor(r){W(this,"strings");let e={[hs.vscode]:{},[hs.jetbrains]:Su};this.strings={...wu,...e[r]}}get(r){return this.strings[r]}}(xu.clientType);function Fr(r,e,t){const n=r.slice();return n[29]=e[t],n}function _u(r){let e;return{c(){e=k("div"),e.textContent="MCP",w(e,"class","section-heading-text")},m(t,n){y(t,e,n)},p:D,d(t){t&&v(e)}}}function Zr(r,e){let t,n,s;return n=new Go({props:{mode:e[1]===e[29].id?"edit":"view",server:e[29],onAdd:e[9],onSave:e[10],onDelete:e[12],onToggleDisableServer:e[13],onEdit:e[8],onCancel:e[6],onJSONImport:e[11],disabledText:e[4].errors.get(e[29].id),warningText:e[4].warnings.get(e[29].id)}}),{key:r,first:null,c(){t=ve(),S(n.$$.fragment),this.first=t},m(o,i){y(o,t,i),x(n,o,i),s=!0},p(o,i){e=o;const l={};10&i[0]&&(l.mode=e[1]===e[29].id?"edit":"view"),8&i[0]&&(l.server=e[29]),24&i[0]&&(l.disabledText=e[4].errors.get(e[29].id)),24&i[0]&&(l.warningText=e[4].warnings.get(e[29].id)),n.$set(l)},i(o){s||(m(n.$$.fragment,o),s=!0)},o(o){g(n.$$.fragment,o),s=!1},d(o){o&&v(t),C(n,o)}}}function Ur(r){let e,t;return e=new Go({props:{mode:r[2],onAdd:r[9],onSave:r[10],onDelete:r[12],onToggleDisableServer:r[13],onEdit:r[8],onCancel:r[6],onJSONImport:r[11]}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,s){const o={};4&s[0]&&(o.mode=n[2]),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function bu(r){let e;return{c(){e=R("Add MCP")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function ku(r){let e,t;return e=new ln({props:{slot:"iconLeft"}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p:D,i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Mu(r){let e;return{c(){e=R("Add remote MCP")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Au(r){let e,t;return e=new ln({props:{slot:"iconLeft"}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p:D,i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Dr(r){let e,t;return e=new we({props:{disabled:r[5],color:"neutral",variant:"soft",size:1,title:"Add MCP from JSON",$$slots:{iconLeft:[Tu],default:[Nu]},$$scope:{ctx:r}}}),e.$on("click",r[24]),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,s){const o={};32&s[0]&&(o.disabled=n[5]),2&s[1]&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Nu(r){let e;return{c(){e=R("Import from JSON")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Tu(r){let e,t;return e=new fo({props:{slot:"iconLeft"}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p:D,i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Eu(r){let e,t,n,s,o,i,l,a,c,d,u,f,p,h,$,b,E,I,A,M,T=[],O=new Map;n=new Q({props:{size:1,weight:"regular",color:"secondary",$$slots:{default:[_u]},$$scope:{ctx:r}}}),u=new yu({props:{onMCPServerAdd:r[9],servers:r[3]}});let G=ce(r[3]);const ne=P=>P[29].id;for(let P=0;P<G.length;P+=1){let H=Fr(r,G,P),ie=ne(H);O.set(ie,T[P]=Zr(ie,H))}let z=(r[2]==="add"||r[2]==="addJson"||r[2]==="addRemote")&&Ur(r);b=new we({props:{disabled:r[5],color:"neutral",variant:"soft",size:1,$$slots:{iconLeft:[ku],default:[bu]},$$scope:{ctx:r}}}),b.$on("click",r[22]),I=new we({props:{disabled:r[5],color:"neutral",variant:"soft",size:1,$$slots:{iconLeft:[Au],default:[Mu]},$$scope:{ctx:r}}}),I.$on("click",r[23]);let Z=r[0]&&Dr(r);return{c(){e=k("div"),t=k("div"),S(n.$$.fragment),s=N(),o=k("div"),i=R(`Configure a new Model Context Protocol server to connect Augment to custom tools. Find out more
    about MCP `),l=k("a"),a=R("in the docs"),c=R("."),d=N(),S(u.$$.fragment),f=N();for(let P=0;P<T.length;P+=1)T[P].c();p=N(),z&&z.c(),h=N(),$=k("div"),S(b.$$.fragment),E=N(),S(I.$$.fragment),A=N(),Z&&Z.c(),w(t,"class","section-heading svelte-1vnq4q3"),w(l,"href",r[14]),w(o,"class","description-text svelte-1vnq4q3"),w(e,"class","mcp-servers svelte-1vnq4q3"),w($,"class","add-mcp-button-container svelte-1vnq4q3")},m(P,H){y(P,e,H),_(e,t),x(n,t,null),_(e,s),_(e,o),_(o,i),_(o,l),_(l,a),_(o,c),_(e,d),x(u,e,null),_(e,f);for(let ie=0;ie<T.length;ie+=1)T[ie]&&T[ie].m(e,null);y(P,p,H),z&&z.m(P,H),y(P,h,H),y(P,$,H),x(b,$,null),_($,E),x(I,$,null),_($,A),Z&&Z.m($,null),M=!0},p(P,H){const ie={};2&H[1]&&(ie.$$scope={dirty:H,ctx:P}),n.$set(ie);const F={};8&H[0]&&(F.servers=P[3]),u.$set(F),16218&H[0]&&(G=ce(P[3]),j(),T=Ke(T,H,ne,1,P,G,O,e,Ye,Zr,null,Fr),V()),P[2]==="add"||P[2]==="addJson"||P[2]==="addRemote"?z?(z.p(P,H),4&H[0]&&m(z,1)):(z=Ur(P),z.c(),m(z,1),z.m(h.parentNode,h)):z&&(j(),g(z,1,1,()=>{z=null}),V());const J={};32&H[0]&&(J.disabled=P[5]),2&H[1]&&(J.$$scope={dirty:H,ctx:P}),b.$set(J);const U={};32&H[0]&&(U.disabled=P[5]),2&H[1]&&(U.$$scope={dirty:H,ctx:P}),I.$set(U),P[0]?Z?(Z.p(P,H),1&H[0]&&m(Z,1)):(Z=Dr(P),Z.c(),m(Z,1),Z.m($,null)):Z&&(j(),g(Z,1,1,()=>{Z=null}),V())},i(P){if(!M){m(n.$$.fragment,P),m(u.$$.fragment,P);for(let H=0;H<G.length;H+=1)m(T[H]);m(z),m(b.$$.fragment,P),m(I.$$.fragment,P),m(Z),M=!0}},o(P){g(n.$$.fragment,P),g(u.$$.fragment,P);for(let H=0;H<T.length;H+=1)g(T[H]);g(z),g(b.$$.fragment,P),g(I.$$.fragment,P),g(Z),M=!1},d(P){P&&(v(e),v(p),v(h),v($)),C(n),C(u);for(let H=0;H<T.length;H+=1)T[H].d();z&&z.d(P),C(b),C(I),Z&&Z.d()}}}function Iu(r,e,t){let n,s,o,{onMCPServerAdd:i}=e,{onMCPServerSave:l}=e,{onMCPServerDelete:a}=e,{onMCPServerToggleDisable:c}=e,{onCancel:d}=e,{onMCPServerJSONImport:u}=e,{isMCPImportEnabled:f=!0}=e,p=null,h=null,$=[];const b=Ft(is.key),E=as(),I=b.getEnableNativeRemoteMcp(),A=E.getServers();function M(P){return async function(...H){const ie=await P(...H);return t(2,h=null),t(1,p=null),ie}}Pe(r,A,P=>t(21,o=P));const T=M(i),O=M(l),G=M(u),ne=M(a),z=M(c),Z=Cu.get("mcpDocsURL");return r.$$set=P=>{"onMCPServerAdd"in P&&t(15,i=P.onMCPServerAdd),"onMCPServerSave"in P&&t(16,l=P.onMCPServerSave),"onMCPServerDelete"in P&&t(17,a=P.onMCPServerDelete),"onMCPServerToggleDisable"in P&&t(18,c=P.onMCPServerToggleDisable),"onCancel"in P&&t(19,d=P.onCancel),"onMCPServerJSONImport"in P&&t(20,u=P.onMCPServerJSONImport),"isMCPImportEnabled"in P&&t(0,f=P.isMCPImportEnabled)},r.$$.update=()=>{6&r.$$.dirty[0]&&t(5,n=h==="add"||h==="addJson"||h==="addRemote"||p!==null),2097152&r.$$.dirty[0]&&t(3,$=I?ui(o):o),8&r.$$.dirty[0]&&t(4,s=Ln.parseServerValidationMessages($))},[f,p,h,$,s,n,function(){t(1,p=null),t(2,h=null),d==null||d()},A,function(P){t(1,p=P.id)},T,O,G,ne,z,Z,i,l,a,c,d,u,o,()=>{t(2,h="add")},()=>{t(2,h="addRemote")},()=>{t(2,h="addJson")}]}class Ru extends ae{constructor(e){super(),le(this,e,Iu,Eu,oe,{onMCPServerAdd:15,onMCPServerSave:16,onMCPServerDelete:17,onMCPServerToggleDisable:18,onCancel:19,onMCPServerJSONImport:20,isMCPImportEnabled:0},null,[-1,-1])}}function jr(r,e,t){const n=r.slice();return n[12]=e[t],n}function Pu(r){let e;return{c(){e=k("div"),e.textContent="Terminal",w(e,"class","section-heading-text")},m(t,n){y(t,e,n)},p:D,d(t){t&&v(e)}}}function Lu(r){let e;return{c(){e=R("Shell:")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Ou(r){let e;return{c(){e=R("Select a shell")},m(t,n){y(t,e,n)},p:D,d(t){t&&v(e)}}}function zu(r){let e;return{c(){e=R("No shells available")},m(t,n){y(t,e,n)},p:D,d(t){t&&v(e)}}}function Fu(r){let e,t,n,s,o=r[5].friendlyName+"",i=r[5].supportString+"";return{c(){e=R(o),t=R(`
            (`),n=R(i),s=R(")")},m(l,a){y(l,e,a),y(l,t,a),y(l,n,a),y(l,s,a)},p(l,a){32&a&&o!==(o=l[5].friendlyName+"")&&de(e,o),32&a&&i!==(i=l[5].supportString+"")&&de(n,i)},d(l){l&&(v(e),v(t),v(n),v(s))}}}function Zu(r){let e;function t(o,i){return o[5]&&o[1].length>0?Fu:o[1].length===0?zu:Ou}let n=t(r),s=n(r);return{c(){s.c(),e=ve()},m(o,i){s.m(o,i),y(o,e,i)},p(o,i){n===(n=t(o))&&s?s.p(o,i):(s.d(1),s=n(o),s&&(s.c(),s.m(e.parentNode,e)))},d(o){o&&v(e),s.d(o)}}}function Uu(r){let e,t;return e=new pi({props:{slot:"iconRight"}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p:D,i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Du(r){let e,t;return e=new we({props:{size:1,variant:"outline",color:"neutral",disabled:r[1].length===0,$$slots:{iconRight:[Uu],default:[Zu]},$$scope:{ctx:r}}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,s){const o={};2&s&&(o.disabled=n[1].length===0),32802&s&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function ju(r){let e,t;return e=new xe.Label({props:{$$slots:{default:[qu]},$$scope:{ctx:r}}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,s){const o={};32768&s&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Vu(r){let e,t,n=[],s=new Map,o=ce(r[1]);const i=l=>l[12].friendlyName;for(let l=0;l<o.length;l+=1){let a=jr(r,o,l),c=i(a);s.set(c,n[l]=Vr(c,a))}return{c(){for(let l=0;l<n.length;l+=1)n[l].c();e=ve()},m(l,a){for(let c=0;c<n.length;c+=1)n[c]&&n[c].m(l,a);y(l,e,a),t=!0},p(l,a){30&a&&(o=ce(l[1]),j(),n=Ke(n,a,i,1,l,o,s,e.parentNode,Ye,Vr,e,jr),V())},i(l){if(!t){for(let a=0;a<o.length;a+=1)m(n[a]);t=!0}},o(l){for(let a=0;a<n.length;a+=1)g(n[a]);t=!1},d(l){l&&v(e);for(let a=0;a<n.length;a+=1)n[a].d(l)}}}function qu(r){let e;return{c(){e=R("No shells available")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Hu(r){let e,t,n,s,o=r[12].friendlyName+"",i=r[12].supportString+"";return{c(){e=R(o),t=R(`
              (`),n=R(i),s=R(`)
            `)},m(l,a){y(l,e,a),y(l,t,a),y(l,n,a),y(l,s,a)},p(l,a){2&a&&o!==(o=l[12].friendlyName+"")&&de(e,o),2&a&&i!==(i=l[12].supportString+"")&&de(n,i)},d(l){l&&(v(e),v(t),v(n),v(s))}}}function Vr(r,e){let t,n,s;function o(){return e[8](e[12])}return n=new xe.Item({props:{onSelect:o,highlight:e[2]===e[12].friendlyName,$$slots:{default:[Hu]},$$scope:{ctx:e}}}),{key:r,first:null,c(){t=ve(),S(n.$$.fragment),this.first=t},m(i,l){y(i,t,l),x(n,i,l),s=!0},p(i,l){e=i;const a={};26&l&&(a.onSelect=o),6&l&&(a.highlight=e[2]===e[12].friendlyName),32770&l&&(a.$$scope={dirty:l,ctx:e}),n.$set(a)},i(i){s||(m(n.$$.fragment,i),s=!0)},o(i){g(n.$$.fragment,i),s=!1},d(i){i&&v(t),C(n,i)}}}function Bu(r){let e,t,n,s;const o=[Vu,ju],i=[];function l(a,c){return a[1].length>0?0:1}return e=l(r),t=i[e]=o[e](r),{c(){t.c(),n=ve()},m(a,c){i[e].m(a,c),y(a,n,c),s=!0},p(a,c){let d=e;e=l(a),e===d?i[e].p(a,c):(j(),g(i[d],1,1,()=>{i[d]=null}),V(),t=i[e],t?t.p(a,c):(t=i[e]=o[e](a),t.c()),m(t,1),t.m(n.parentNode,n))},i(a){s||(m(t),s=!0)},o(a){g(t),s=!1},d(a){a&&v(n),i[e].d(a)}}}function Gu(r){let e,t,n,s;return e=new xe.Trigger({props:{$$slots:{default:[Du]},$$scope:{ctx:r}}}),n=new xe.Content({props:{side:"bottom",align:"start",$$slots:{default:[Bu]},$$scope:{ctx:r}}}),{c(){S(e.$$.fragment),t=N(),S(n.$$.fragment)},m(o,i){x(e,o,i),y(o,t,i),x(n,o,i),s=!0},p(o,i){const l={};32802&i&&(l.$$scope={dirty:i,ctx:o}),e.$set(l);const a={};32798&i&&(a.$$scope={dirty:i,ctx:o}),n.$set(a)},i(o){s||(m(e.$$.fragment,o),m(n.$$.fragment,o),s=!0)},o(o){g(e.$$.fragment,o),g(n.$$.fragment,o),s=!1},d(o){o&&v(t),C(e,o),C(n,o)}}}function Ju(r){let e;return{c(){e=R("Start-up script: Code to run wherever a new terminal is opened")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Wu(r){let e,t,n,s,o,i,l,a,c,d,u,f,p,h,$;function b(M){r[9](M)}t=new Q({props:{size:1,weight:"regular",color:"secondary",$$slots:{default:[Pu]},$$scope:{ctx:r}}}),o=new Q({props:{size:1,$$slots:{default:[Lu]},$$scope:{ctx:r}}});let E={$$slots:{default:[Gu]},$$scope:{ctx:r}};function I(M){r[10](M)}r[4]!==void 0&&(E.requestClose=r[4]),l=new xe.Root({props:E}),be.push(()=>ke(l,"requestClose",b)),u=new Q({props:{size:1,$$slots:{default:[Ju]},$$scope:{ctx:r}}});let A={placeholder:"Enter shell commands to run on terminal startup",resize:"vertical"};return r[0]!==void 0&&(A.value=r[0]),p=new ko({props:A}),be.push(()=>ke(p,"value",I)),p.$on("change",r[6]),{c(){e=k("div"),S(t.$$.fragment),n=N(),s=k("div"),S(o.$$.fragment),i=N(),S(l.$$.fragment),c=N(),d=k("div"),S(u.$$.fragment),f=N(),S(p.$$.fragment),w(s,"class","shell-selector svelte-dndd5n"),w(d,"class","startup-script-container svelte-dndd5n"),w(e,"class","terminal-settings svelte-dndd5n")},m(M,T){y(M,e,T),x(t,e,null),_(e,n),_(e,s),x(o,s,null),_(s,i),x(l,s,null),_(e,c),_(e,d),x(u,d,null),_(d,f),x(p,d,null),$=!0},p(M,[T]){const O={};32768&T&&(O.$$scope={dirty:T,ctx:M}),t.$set(O);const G={};32768&T&&(G.$$scope={dirty:T,ctx:M}),o.$set(G);const ne={};32830&T&&(ne.$$scope={dirty:T,ctx:M}),!a&&16&T&&(a=!0,ne.requestClose=M[4],Me(()=>a=!1)),l.$set(ne);const z={};32768&T&&(z.$$scope={dirty:T,ctx:M}),u.$set(z);const Z={};!h&&1&T&&(h=!0,Z.value=M[0],Me(()=>h=!1)),p.$set(Z)},i(M){$||(m(t.$$.fragment,M),m(o.$$.fragment,M),m(l.$$.fragment,M),m(u.$$.fragment,M),m(p.$$.fragment,M),$=!0)},o(M){g(t.$$.fragment,M),g(o.$$.fragment,M),g(l.$$.fragment,M),g(u.$$.fragment,M),g(p.$$.fragment,M),$=!1},d(M){M&&v(e),C(t),C(o),C(l),C(u),C(p)}}}function Ku(r,e,t){let n,s,{supportedShells:o=[]}=e,{selectedShell:i}=e,{startupScript:l}=e,{onShellSelect:a}=e,{onStartupScriptChange:c}=e;return r.$$set=d=>{"supportedShells"in d&&t(1,o=d.supportedShells),"selectedShell"in d&&t(2,i=d.selectedShell),"startupScript"in d&&t(0,l=d.startupScript),"onShellSelect"in d&&t(3,a=d.onShellSelect),"onStartupScriptChange"in d&&t(7,c=d.onStartupScriptChange)},r.$$.update=()=>{var d;4&r.$$.dirty&&t(5,n=i?(d=i,o.find(u=>u.friendlyName===d)):void 0)},[l,o,i,a,s,n,function(d){const u=d.target;c(u.value)},c,d=>{a(d.friendlyName),s()},function(d){s=d,t(4,s)},function(d){l=d,t(0,l)}]}class Yu extends ae{constructor(e){super(),le(this,e,Ku,Wu,oe,{supportedShells:1,selectedShell:2,startupScript:0,onShellSelect:3,onStartupScriptChange:7})}}function Xu(r){let e;return{c(){e=k("div"),e.textContent="Sound Settings",w(e,"class","section-heading-text")},m(t,n){y(t,e,n)},p:D,d(t){t&&v(e)}}}function Qu(r){let e;return{c(){e=R("Enable Sound Effects")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function ep(r){let e;return{c(){e=R("Play a sound when an agent completes a task")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function tp(r){let e,t,n,s,o,i,l;return n=new Q({props:{size:2,weight:"medium",$$slots:{default:[Qu]},$$scope:{ctx:r}}}),i=new Q({props:{size:1,weight:"medium",$$slots:{default:[ep]},$$scope:{ctx:r}}}),{c(){e=k("div"),t=k("div"),S(n.$$.fragment),s=N(),o=k("div"),S(i.$$.fragment),w(e,"class","c-sound-setting__info svelte-8awonv"),w(e,"slot","header-left")},m(a,c){y(a,e,c),_(e,t),x(n,t,null),_(e,s),_(e,o),x(i,o,null),l=!0},p(a,c){const d={};64&c&&(d.$$scope={dirty:c,ctx:a}),n.$set(d);const u={};64&c&&(u.$$scope={dirty:c,ctx:a}),i.$set(u)},i(a){l||(m(n.$$.fragment,a),m(i.$$.fragment,a),l=!0)},o(a){g(n.$$.fragment,a),g(i.$$.fragment,a),l=!1},d(a){a&&v(e),C(n),C(i)}}}function np(r){let e,t,n;return t=new ts({props:{size:1,checked:r[0]}}),t.$on("change",r[5]),{c(){e=k("div"),S(t.$$.fragment),w(e,"slot","header-right")},m(s,o){y(s,e,o),x(t,e,null),n=!0},p(s,o){const i={};1&o&&(i.checked=s[0]),t.$set(i)},i(s){n||(m(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&v(e),C(t)}}}function qr(r){let e,t;return e=new wt({props:{$$slots:{"header-right":[lp],"header-left":[op]},$$scope:{ctx:r}}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,s){const o={};65&s&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function sp(r){let e;return{c(){e=R("Test Sound")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function rp(r){let e;return{c(){e=R("Play a sample of the agent completion sound")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function op(r){let e,t,n,s,o,i,l;return n=new Q({props:{size:2,weight:"medium",$$slots:{default:[sp]},$$scope:{ctx:r}}}),i=new Q({props:{size:1,weight:"medium",$$slots:{default:[rp]},$$scope:{ctx:r}}}),{c(){e=k("div"),t=k("div"),S(n.$$.fragment),s=N(),o=k("div"),S(i.$$.fragment),w(e,"class","c-sound-setting__info svelte-8awonv"),w(e,"slot","header-left")},m(a,c){y(a,e,c),_(e,t),x(n,t,null),_(e,s),_(e,o),x(i,o,null),l=!0},p(a,c){const d={};64&c&&(d.$$scope={dirty:c,ctx:a}),n.$set(d);const u={};64&c&&(u.$$scope={dirty:c,ctx:a}),i.$set(u)},i(a){l||(m(n.$$.fragment,a),m(i.$$.fragment,a),l=!0)},o(a){g(n.$$.fragment,a),g(i.$$.fragment,a),l=!1},d(a){a&&v(e),C(n),C(i)}}}function ip(r){let e;return{c(){e=R("Play")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function ap(r){let e,t;return e=new Ai({props:{size:1,defaultColor:"neutral",enabled:r[0],stickyColor:!1,disabled:!r[0],onClick:r[3],tooltip:{neutral:"Play a sample of the agent completion sound",success:"Played!"},$$slots:{default:[ip]},$$scope:{ctx:r}}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,s){const o={};1&s&&(o.enabled=n[0]),1&s&&(o.disabled=!n[0]),64&s&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function lp(r){let e,t,n;return t=new rt({props:{content:r[0]?"":"Enable sound effects to test",triggerOn:[ns.Hover],$$slots:{default:[ap]},$$scope:{ctx:r}}}),{c(){e=k("div"),S(t.$$.fragment),w(e,"slot","header-right")},m(s,o){y(s,e,o),x(t,e,null),n=!0},p(s,o){const i={};1&o&&(i.content=s[0]?"":"Enable sound effects to test"),65&o&&(i.$$scope={dirty:o,ctx:s}),t.$set(i)},i(s){n||(m(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&v(e),C(t)}}}function cp(r){let e,t,n,s,o,i;e=new Q({props:{size:1,weight:"regular",color:"secondary",$$slots:{default:[Xu]},$$scope:{ctx:r}}}),s=new wt({props:{$$slots:{"header-right":[np],"header-left":[tp]},$$scope:{ctx:r}}});let l=r[0]&&qr(r);return{c(){S(e.$$.fragment),t=N(),n=k("div"),S(s.$$.fragment),o=N(),l&&l.c(),w(n,"class","c-sound-settings svelte-8awonv")},m(a,c){x(e,a,c),y(a,t,c),y(a,n,c),x(s,n,null),_(n,o),l&&l.m(n,null),i=!0},p(a,[c]){const d={};64&c&&(d.$$scope={dirty:c,ctx:a}),e.$set(d);const u={};65&c&&(u.$$scope={dirty:c,ctx:a}),s.$set(u),a[0]?l?(l.p(a,c),1&c&&m(l,1)):(l=qr(a),l.c(),m(l,1),l.m(n,null)):l&&(j(),g(l,1,1,()=>{l=null}),V())},i(a){i||(m(e.$$.fragment,a),m(s.$$.fragment,a),m(l),i=!0)},o(a){g(e.$$.fragment,a),g(s.$$.fragment,a),g(l),i=!1},d(a){a&&(v(t),v(n)),C(e,a),C(s),l&&l.d()}}}function dp(r,e,t){let n,s,o,i=D;r.$$.on_destroy.push(()=>i());const l=Ft(si.key);return r.$$.update=()=>{16&r.$$.dirty&&t(0,s=o.enabled)},t(1,n=l.getCurrentSettings),i(),i=an(n,a=>t(4,o=a)),[s,n,l,async function(){return await l.playAgentComplete(),"success"},o,()=>l.updateEnabled(!s)]}class up extends ae{constructor(e){super(),le(this,e,dp,cp,oe,{})}}const Bt=class Bt{constructor(e){W(this,"_swarmModeSettings",ye(ct));W(this,"_isLoaded",!1);W(this,"_pollInterval",null);W(this,"_lastKnownSettingsHash","");W(this,"dispose",()=>{this.stopPolling()});this._msgBroker=e,this.initialize(),this.startPolling()}get getCurrentSettings(){return this._swarmModeSettings}async initialize(){if(!this._isLoaded)try{const e=await this._msgBroker.sendToSidecar({type:st.getSwarmModeSettings});e.data&&(this._swarmModeSettings.set(e.data),this._lastKnownSettingsHash=JSON.stringify(e.data)),this._isLoaded=!0}catch(e){console.warn("Failed to load swarm mode settings, using defaults:",e),this._swarmModeSettings.set(ct),this._lastKnownSettingsHash=JSON.stringify(ct),this._isLoaded=!0}}async updateSettings(e){try{const t=await this._msgBroker.sendToSidecar({type:st.updateSwarmModeSettings,data:e});t.data&&(this._swarmModeSettings.set(t.data),this._lastKnownSettingsHash=JSON.stringify(t.data))}catch(t){throw console.error("Failed to update swarm mode settings:",t),t}}async setEnabled(e){await this.updateSettings({enabled:e})}async resetToDefaults(){await this.updateSettings(ct)}updateEnabled(e){this.setEnabled(e).catch(t=>{console.error("Failed to update enabled setting:",t)})}startPolling(){this._pollInterval=setInterval(()=>{this.checkForUpdates()},Bt.POLLING_INTERVAL_MS)}stopPolling(){this._pollInterval!==null&&(clearInterval(this._pollInterval),this._pollInterval=null)}async checkForUpdates(){try{const e=await this._msgBroker.sendToSidecar({type:st.getSwarmModeSettings}),t=JSON.stringify(e.data);this._lastKnownSettingsHash&&t!==this._lastKnownSettingsHash&&e.data&&this._swarmModeSettings.set(e.data),this._lastKnownSettingsHash=t}catch(e){console.warn("Failed to check for swarm mode settings updates:",e)}}};W(Bt,"key","swarmModeModel"),W(Bt,"POLLING_INTERVAL_MS",5e3);let Yn=Bt;function Hr(r){let e,t,n,s,o;return e=new Q({props:{size:1,weight:"regular",color:"secondary",$$slots:{default:[pp]},$$scope:{ctx:r}}}),s=new wt({props:{$$slots:{"header-right":[$p],"header-left":[gp]},$$scope:{ctx:r}}}),{c(){S(e.$$.fragment),t=N(),n=k("div"),S(s.$$.fragment),w(n,"class","c-agent-settings svelte-wzfxyl")},m(i,l){x(e,i,l),y(i,t,l),y(i,n,l),x(s,n,null),o=!0},p(i,l){const a={};128&l&&(a.$$scope={dirty:l,ctx:i}),e.$set(a);const c={};132&l&&(c.$$scope={dirty:l,ctx:i}),s.$set(c)},i(i){o||(m(e.$$.fragment,i),m(s.$$.fragment,i),o=!0)},o(i){g(e.$$.fragment,i),g(s.$$.fragment,i),o=!1},d(i){i&&(v(t),v(n)),C(e,i),C(s)}}}function pp(r){let e;return{c(){e=k("div"),e.textContent="Agent Settings",w(e,"class","section-heading-text")},m(t,n){y(t,e,n)},p:D,d(t){t&&v(e)}}}function mp(r){let e;return{c(){e=R("Enable Swarm Mode")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function fp(r){let e;return{c(){e=R("Allow agents to coordinate and work together on complex tasks")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function gp(r){let e,t,n,s,o,i,l;return n=new Q({props:{size:2,weight:"medium",$$slots:{default:[mp]},$$scope:{ctx:r}}}),i=new Q({props:{size:1,weight:"medium",$$slots:{default:[fp]},$$scope:{ctx:r}}}),{c(){e=k("div"),t=k("div"),S(n.$$.fragment),s=N(),o=k("div"),S(i.$$.fragment),w(e,"class","c-agent-setting__info svelte-wzfxyl"),w(e,"slot","header-left")},m(a,c){y(a,e,c),_(e,t),x(n,t,null),_(e,s),_(e,o),x(i,o,null),l=!0},p(a,c){const d={};128&c&&(d.$$scope={dirty:c,ctx:a}),n.$set(d);const u={};128&c&&(u.$$scope={dirty:c,ctx:a}),i.$set(u)},i(a){l||(m(n.$$.fragment,a),m(i.$$.fragment,a),l=!0)},o(a){g(n.$$.fragment,a),g(i.$$.fragment,a),l=!1},d(a){a&&v(e),C(n),C(i)}}}function $p(r){let e,t,n;return t=new ts({props:{size:1,checked:r[2]}}),t.$on("change",r[6]),{c(){e=k("div"),S(t.$$.fragment),w(e,"slot","header-right")},m(s,o){y(s,e,o),x(t,e,null),n=!0},p(s,o){const i={};4&o&&(i.checked=s[2]),t.$set(i)},i(s){n||(m(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&v(e),C(t)}}}function hp(r){let e,t,n=r[0]&&r[1]&&Hr(r);return{c(){n&&n.c(),e=ve()},m(s,o){n&&n.m(s,o),y(s,e,o),t=!0},p(s,[o]){s[0]&&s[1]?n?(n.p(s,o),3&o&&m(n,1)):(n=Hr(s),n.c(),m(n,1),n.m(e.parentNode,e)):n&&(j(),g(n,1,1,()=>{n=null}),V())},i(s){t||(m(n),t=!0)},o(s){g(n),t=!1},d(s){s&&v(e),n&&n.d(s)}}}function vp(r,e,t){let n,s,o,i=D;r.$$.on_destroy.push(()=>i());let{isSwarmModeEnabled:l=!1}=e,{hasEverUsedRemoteAgent:a=!1}=e;const c=Ft(Yn.key);return r.$$set=d=>{"isSwarmModeEnabled"in d&&t(0,l=d.isSwarmModeEnabled),"hasEverUsedRemoteAgent"in d&&t(1,a=d.hasEverUsedRemoteAgent)},r.$$.update=()=>{32&r.$$.dirty&&t(2,s=o.enabled)},t(3,n=c.getCurrentSettings),i(),i=an(n,d=>t(5,o=d)),[l,a,s,n,c,o,()=>c.updateEnabled(!s)]}class yp extends ae{constructor(e){super(),le(this,e,vp,hp,oe,{isSwarmModeEnabled:0,hasEverUsedRemoteAgent:1})}}function Br(r){let e,t;return e=new Ru({props:{onMCPServerAdd:r[11],onMCPServerSave:r[12],onMCPServerDelete:r[13],onMCPServerToggleDisable:r[14],onMCPServerJSONImport:r[15],onCancel:r[16],isMCPImportEnabled:r[2]}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,s){const o={};2048&s&&(o.onMCPServerAdd=n[11]),4096&s&&(o.onMCPServerSave=n[12]),8192&s&&(o.onMCPServerDelete=n[13]),16384&s&&(o.onMCPServerToggleDisable=n[14]),32768&s&&(o.onMCPServerJSONImport=n[15]),65536&s&&(o.onCancel=n[16]),4&s&&(o.isMCPImportEnabled=n[2]),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Gr(r){let e,t;return e=new Yu({props:{supportedShells:r[17],selectedShell:r[18],startupScript:r[19],onShellSelect:r[20],onStartupScriptChange:r[21]}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,s){const o={};131072&s&&(o.supportedShells=n[17]),262144&s&&(o.selectedShell=n[18]),524288&s&&(o.startupScript=n[19]),1048576&s&&(o.onShellSelect=n[20]),2097152&s&&(o.onStartupScriptChange=n[21]),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Jr(r){let e,t;return e=new up({}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Wr(r){let e,t;return e=new yp({props:{isSwarmModeEnabled:r[6],hasEverUsedRemoteAgent:r[7]}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,s){const o={};64&s&&(o.isSwarmModeEnabled=n[6]),128&s&&(o.hasEverUsedRemoteAgent=n[7]),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function wp(r){let e,t,n,s,o,i,l;t=new Gc({props:{title:"Services",tools:r[0],onAuthenticate:r[8],onRevokeAccess:r[9],onToolApprovalConfigChange:r[10]}});let a=r[1]&&Br(r),c=r[3]&&Gr(r),d=r[4]&&Jr(),u=r[5]&&Wr(r);return{c(){e=k("div"),S(t.$$.fragment),n=N(),a&&a.c(),s=N(),c&&c.c(),o=N(),d&&d.c(),i=N(),u&&u.c(),w(e,"class","c-settings-tools svelte-181yusq")},m(f,p){y(f,e,p),x(t,e,null),_(e,n),a&&a.m(e,null),_(e,s),c&&c.m(e,null),_(e,o),d&&d.m(e,null),_(e,i),u&&u.m(e,null),l=!0},p(f,[p]){const h={};1&p&&(h.tools=f[0]),256&p&&(h.onAuthenticate=f[8]),512&p&&(h.onRevokeAccess=f[9]),1024&p&&(h.onToolApprovalConfigChange=f[10]),t.$set(h),f[1]?a?(a.p(f,p),2&p&&m(a,1)):(a=Br(f),a.c(),m(a,1),a.m(e,s)):a&&(j(),g(a,1,1,()=>{a=null}),V()),f[3]?c?(c.p(f,p),8&p&&m(c,1)):(c=Gr(f),c.c(),m(c,1),c.m(e,o)):c&&(j(),g(c,1,1,()=>{c=null}),V()),f[4]?d?16&p&&m(d,1):(d=Jr(),d.c(),m(d,1),d.m(e,i)):d&&(j(),g(d,1,1,()=>{d=null}),V()),f[5]?u?(u.p(f,p),32&p&&m(u,1)):(u=Wr(f),u.c(),m(u,1),u.m(e,null)):u&&(j(),g(u,1,1,()=>{u=null}),V())},i(f){l||(m(t.$$.fragment,f),m(a),m(c),m(d),m(u),l=!0)},o(f){g(t.$$.fragment,f),g(a),g(c),g(d),g(u),l=!1},d(f){f&&v(e),C(t),a&&a.d(),c&&c.d(),d&&d.d(),u&&u.d()}}}function Sp(r,e,t){let{tools:n=[]}=e,{isMCPEnabled:s=!0}=e,{isMCPImportEnabled:o=!0}=e,{isTerminalEnabled:i=!0}=e,{isSoundCategoryEnabled:l=!1}=e,{isAgentCategoryEnabled:a=!1}=e,{isSwarmModeFeatureFlagEnabled:c=!1}=e,{hasEverUsedRemoteAgent:d=!1}=e,{onAuthenticate:u}=e,{onRevokeAccess:f}=e,{onToolApprovalConfigChange:p=()=>{}}=e,{onMCPServerAdd:h}=e,{onMCPServerSave:$}=e,{onMCPServerDelete:b}=e,{onMCPServerToggleDisable:E}=e,{onMCPServerJSONImport:I}=e,{onCancel:A}=e,{supportedShells:M=[]}=e,{selectedShell:T}=e,{startupScript:O}=e,{onShellSelect:G=()=>{}}=e,{onStartupScriptChange:ne=()=>{}}=e;return r.$$set=z=>{"tools"in z&&t(0,n=z.tools),"isMCPEnabled"in z&&t(1,s=z.isMCPEnabled),"isMCPImportEnabled"in z&&t(2,o=z.isMCPImportEnabled),"isTerminalEnabled"in z&&t(3,i=z.isTerminalEnabled),"isSoundCategoryEnabled"in z&&t(4,l=z.isSoundCategoryEnabled),"isAgentCategoryEnabled"in z&&t(5,a=z.isAgentCategoryEnabled),"isSwarmModeFeatureFlagEnabled"in z&&t(6,c=z.isSwarmModeFeatureFlagEnabled),"hasEverUsedRemoteAgent"in z&&t(7,d=z.hasEverUsedRemoteAgent),"onAuthenticate"in z&&t(8,u=z.onAuthenticate),"onRevokeAccess"in z&&t(9,f=z.onRevokeAccess),"onToolApprovalConfigChange"in z&&t(10,p=z.onToolApprovalConfigChange),"onMCPServerAdd"in z&&t(11,h=z.onMCPServerAdd),"onMCPServerSave"in z&&t(12,$=z.onMCPServerSave),"onMCPServerDelete"in z&&t(13,b=z.onMCPServerDelete),"onMCPServerToggleDisable"in z&&t(14,E=z.onMCPServerToggleDisable),"onMCPServerJSONImport"in z&&t(15,I=z.onMCPServerJSONImport),"onCancel"in z&&t(16,A=z.onCancel),"supportedShells"in z&&t(17,M=z.supportedShells),"selectedShell"in z&&t(18,T=z.selectedShell),"startupScript"in z&&t(19,O=z.startupScript),"onShellSelect"in z&&t(20,G=z.onShellSelect),"onStartupScriptChange"in z&&t(21,ne=z.onStartupScriptChange)},[n,s,o,i,l,a,c,d,u,f,p,h,$,b,E,I,A,M,T,O,G,ne]}class xp extends ae{constructor(e){super(),le(this,e,Sp,wp,oe,{tools:0,isMCPEnabled:1,isMCPImportEnabled:2,isTerminalEnabled:3,isSoundCategoryEnabled:4,isAgentCategoryEnabled:5,isSwarmModeFeatureFlagEnabled:6,hasEverUsedRemoteAgent:7,onAuthenticate:8,onRevokeAccess:9,onToolApprovalConfigChange:10,onMCPServerAdd:11,onMCPServerSave:12,onMCPServerDelete:13,onMCPServerToggleDisable:14,onMCPServerJSONImport:15,onCancel:16,supportedShells:17,selectedShell:18,startupScript:19,onShellSelect:20,onStartupScriptChange:21})}}function Cp(r){let e,t,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 576 512"},r[0]],s={};for(let o=0;o<n.length;o+=1)s=Ae(s,n[o]);return{c(){e=_e("svg"),t=new Mn(!0),this.h()},l(o){e=An(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=Nn(e);t=Tn(i,!0),i.forEach(v),this.h()},h(){t.a=null,ft(e,s)},m(o,i){En(o,e,i),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M288 0c-8.5 0-17 1.7-24.8 5.1L53.9 94.8C40.6 100.5 32 113.5 32 128s8.6 27.5 21.9 33.2l209.3 89.7c7.8 3.4 16.3 5.1 24.8 5.1s17-1.7 24.8-5.1l209.3-89.7c13.3-5.7 21.9-18.8 21.9-33.2s-8.6-27.5-21.9-33.2L312.8 5.1C305 1.7 296.5 0 288 0m-5.9 49.2c1.9-.8 3.9-1.2 5.9-1.2s4 .4 5.9 1.2L477.7 128l-183.8 78.8c-1.9.8-3.9 1.2-5.9 1.2s-4-.4-5.9-1.2L98.3 128zM53.9 222.8C40.6 228.5 32 241.5 32 256s8.6 27.5 21.9 33.2l209.3 89.7c7.8 3.4 16.3 5.1 24.8 5.1s17-1.7 24.8-5.1l209.3-89.7c13.3-5.7 21.9-18.8 21.9-33.2s-8.6-27.5-21.9-33.2l-31.2-13.4-60.9 26.1 47.7 20.5-183.8 78.8c-1.9.8-3.9 1.2-5.9 1.2s-4-.4-5.9-1.2L98.3 256l47.7-20.5-60.9-26.1zm0 128C40.6 356.5 32 369.5 32 384s8.6 27.5 21.9 33.2l209.3 89.7c7.8 3.4 16.3 5.1 24.8 5.1s17-1.7 24.8-5.1l209.3-89.7c13.3-5.7 21.9-18.8 21.9-33.2s-8.6-27.5-21.9-33.2l-31.2-13.4-60.9 26.1 47.7 20.5-183.8 78.8c-1.9.8-3.9 1.2-5.9 1.2s-4-.4-5.9-1.2L98.3 384l47.7-20.5-60.9-26.1z"/>',e)},p(o,[i]){ft(e,s=pt(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 576 512"},1&i&&o[0]]))},i:D,o:D,d(o){o&&v(e)}}}function _p(r,e,t){return r.$$set=n=>{t(0,e=Ae(Ae({},e),We(n)))},[e=We(e)]}class bp extends ae{constructor(e){super(),le(this,e,_p,Cp,oe,{})}}function kp(r){let e,t,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},r[0]],s={};for(let o=0;o<n.length;o+=1)s=Ae(s,n[o]);return{c(){e=_e("svg"),t=new Mn(!0),this.h()},l(o){e=An(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=Nn(e);t=Tn(i,!0),i.forEach(v),this.h()},h(){t.a=null,ft(e,s)},m(o,i){En(o,e,i),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M63.2 379.3c-6.2-6.2-6.2-16.4 0-22.6l39.4-39.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 39.4-39.4c6.2-6.2 16.4-6.2 22.6 0l69.5 69.5c6.2 6.2 6.2 16.4 0 22.6L155.3 448.8c-6.2 6.2-16.4 6.2-22.6 0zm35.5 103.4c25 25 65.5 25 90.5 0l293.5-293.4c25-25 25-65.5 0-90.5l-69.4-69.5c-25-25-65.5-25-90.5 0L29.3 322.7c-25 25-25 65.5 0 90.5l69.5 69.5z"/>',e)},p(o,[i]){ft(e,s=pt(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&i&&o[0]]))},i:D,o:D,d(o){o&&v(e)}}}function Mp(r,e,t){return r.$$set=n=>{t(0,e=Ae(Ae({},e),We(n)))},[e=We(e)]}class Ap extends ae{constructor(e){super(),le(this,e,Mp,kp,oe,{})}}function Np(r){let e,t,n,s;function o(l){r[6](l)}let i={placeholder:"Add your guidelines for Augment Chat...",resize:"vertical",saveFunction:r[3]};return r[0]!==void 0&&(i.value=r[0]),t=new Ii({props:i}),be.push(()=>ke(t,"value",o)),t.$on("focus",r[7]),{c(){e=k("div"),S(t.$$.fragment),w(e,"class","c-user-guidelines-category__input svelte-10borzo")},m(l,a){y(l,e,a),x(t,e,null),s=!0},p(l,[a]){const c={};!n&&1&a&&(n=!0,c.value=l[0],Me(()=>n=!1)),t.$set(c)},i(l){s||(m(t.$$.fragment,l),s=!0)},o(l){g(t.$$.fragment,l),s=!1},d(l){l&&v(e),C(t)}}}function Tp(r,e,t){let n;const s=Qn();let{userGuidelines:o=""}=e,{userGuidelinesLengthLimit:i}=e,{updateUserGuideline:l=()=>!1}=e;const a=ye(void 0);function c(){const d=o.trim();if(n!==d){if(!l(d))throw i&&d.length>i?`The user guideline must be less than ${i} character long`:"An error occurred updating the user";$s(a,n=d,n)}}return Pe(r,a,d=>t(8,n=d)),uo(()=>{$s(a,n=o.trim(),n)}),po(()=>{c()}),r.$$set=d=>{"userGuidelines"in d&&t(0,o=d.userGuidelines),"userGuidelinesLengthLimit"in d&&t(4,i=d.userGuidelinesLengthLimit),"updateUserGuideline"in d&&t(5,l=d.updateUserGuideline)},[o,s,a,c,i,l,function(d){o=d,t(0,o)},d=>{s("focus",d)}]}class Jo extends ae{constructor(e){super(),le(this,e,Tp,Np,oe,{userGuidelines:0,userGuidelinesLengthLimit:4,updateUserGuideline:5})}}class Ep{constructor(e,t,n){W(this,"_showCreateRuleDialog",ye(!1));W(this,"_createRuleError",ye(""));W(this,"_extensionClient");this._host=e,this._msgBroker=t,this._rulesModel=n;const s=new Pi;this._extensionClient=new Li(e,t,s)}async createRule(){this._showCreateRuleDialog.set(!0)}async handleCreateRuleWithName(e){if(e&&e.trim()){this._createRuleError.set("");try{const t=await this._rulesModel.createRule(e.trim());t&&t.path&&await this.openRule(t.path),this._extensionClient.reportAgentSessionEvent({eventName:Dn.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:pn.manuallyCreated,numFiles:1,source:""}}}),this.hideCreateRuleDialog()}catch{const n=`Failed to create rule "${e.trim()}"`;this._createRuleError.set(n)}}else this.hideCreateRuleDialog()}async openRule(e){try{const t=await this._rulesModel.getWorkspaceRoot();e===ys?this._extensionClient.openFile({repoRoot:t,pathName:ys}):this._extensionClient.openFile({repoRoot:t,pathName:`${wi}/${Si}/${e}`})}catch(t){console.error("Failed to open rule:",t)}}async deleteRule(e){try{await this._extensionClient.openConfirmationModal({title:"Delete Rule",message:"Are you sure you want to delete this rule?",confirmButtonText:"Delete",cancelButtonText:"Cancel"})&&await this._rulesModel.deleteRule(e)}catch(t){console.error("Failed to delete rule:",t)}}async selectFileToImport(){try{const e=await this._msgBroker.send({type:ue.triggerImportDialogRequest},1e5);if(e.data.selectedPaths&&e.data.selectedPaths.length>0){const t=await this._rulesModel.processSelectedPaths(e.data.selectedPaths);this._showImportNotification(t),this._reportSelectedImportMetrics(t)}}catch(e){console.error("Failed to import files:",e)}}async getAutoImportOptions(){return await this._rulesModel.getAutoImportOptions()}async processAutoImportSelection(e){const t=await this._rulesModel.processAutoImportSelection(e);return this._showImportNotification(t),this._reportAutoImportMetrics(t),t}_showImportNotification(e){let t;e.importedRulesCount===0?t=e.source?`No new rules imported from ${e.source}`:"No new rules imported":(t=`Successfully imported ${e.importedRulesCount} rule${e.importedRulesCount!==1?"s":""}`,e.duplicatesCount&&e.duplicatesCount>0&&(t+=` and skipped ${e.duplicatesCount} duplicate${e.duplicatesCount!==1?"s":""}`)),this._extensionClient.showNotification({message:t,type:e.importedRulesCount>0?"info":"warning"})}_reportSelectedImportMetrics(e){const t=e.directoryOrFile==="directory"?pn.selectedDirectory:(e.directoryOrFile,pn.selectedFile);this._extensionClient.reportAgentSessionEvent({eventName:Dn.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:t,numFiles:e.importedRulesCount,source:""}}})}_reportAutoImportMetrics(e){this._extensionClient.reportAgentSessionEvent({eventName:Dn.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:pn.auto,numFiles:e.importedRulesCount,source:e.source}}})}getShowCreateRuleDialog(){return this._showCreateRuleDialog}getCreateRuleError(){return this._createRuleError}hideCreateRuleDialog(){this._showCreateRuleDialog.set(!1),this._createRuleError.set("")}}function Ip(r){let e;return{c(){e=R("Enter a name for the new rule file (e.g., architecture.md):")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Kr(r){let e,t;return e=new In({props:{variant:"soft",color:"error",size:1,$$slots:{icon:[Pp],default:[Rp]},$$scope:{ctx:r}}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,s){const o={};4098&s&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Rp(r){let e;return{c(){e=R(r[1])},m(t,n){y(t,e,n)},p(t,n){2&n&&de(e,t[1])},d(t){t&&v(e)}}}function Pp(r){let e,t;return e=new ss({props:{slot:"icon"}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p:D,i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Lp(r){let e,t,n,s,o,i,l,a;function c(p){r[9](p)}function d(p){r[10](p)}e=new Q({props:{size:2,color:"secondary",$$slots:{default:[Ip]},$$scope:{ctx:r}}});let u={placeholder:"rule-name.md",disabled:r[4]};r[3]!==void 0&&(u.value=r[3]),r[2]!==void 0&&(u.textInput=r[2]),n=new bt({props:u}),be.push(()=>ke(n,"value",c)),be.push(()=>ke(n,"textInput",d)),n.$on("keydown",r[8]);let f=r[1]&&Kr(r);return{c(){S(e.$$.fragment),t=N(),S(n.$$.fragment),i=N(),f&&f.c(),l=ve()},m(p,h){x(e,p,h),y(p,t,h),x(n,p,h),y(p,i,h),f&&f.m(p,h),y(p,l,h),a=!0},p(p,h){const $={};4096&h&&($.$$scope={dirty:h,ctx:p}),e.$set($);const b={};16&h&&(b.disabled=p[4]),!s&&8&h&&(s=!0,b.value=p[3],Me(()=>s=!1)),!o&&4&h&&(o=!0,b.textInput=p[2],Me(()=>o=!1)),n.$set(b),p[1]?f?(f.p(p,h),2&h&&m(f,1)):(f=Kr(p),f.c(),m(f,1),f.m(l.parentNode,l)):f&&(j(),g(f,1,1,()=>{f=null}),V())},i(p){a||(m(e.$$.fragment,p),m(n.$$.fragment,p),m(f),a=!0)},o(p){g(e.$$.fragment,p),g(n.$$.fragment,p),g(f),a=!1},d(p){p&&(v(t),v(i),v(l)),C(e,p),C(n,p),f&&f.d(p)}}}function Op(r){let e;return{c(){e=R("Cancel")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function zp(r){let e,t=r[4]?"Creating...":"Create";return{c(){e=R(t)},m(n,s){y(n,e,s)},p(n,s){16&s&&t!==(t=n[4]?"Creating...":"Create")&&de(e,t)},d(n){n&&v(e)}}}function Fp(r){let e,t,n,s,o;return t=new we({props:{variant:"solid",color:"neutral",disabled:r[4],$$slots:{default:[Op]},$$scope:{ctx:r}}}),t.$on("click",r[6]),s=new we({props:{variant:"solid",color:"accent",disabled:!r[3].trim()||r[4],loading:r[4],$$slots:{default:[zp]},$$scope:{ctx:r}}}),s.$on("click",r[5]),{c(){e=k("div"),S(t.$$.fragment),n=N(),S(s.$$.fragment),w(e,"slot","footer")},m(i,l){y(i,e,l),x(t,e,null),_(e,n),x(s,e,null),o=!0},p(i,l){const a={};16&l&&(a.disabled=i[4]),4096&l&&(a.$$scope={dirty:l,ctx:i}),t.$set(a);const c={};24&l&&(c.disabled=!i[3].trim()||i[4]),16&l&&(c.loading=i[4]),4112&l&&(c.$$scope={dirty:l,ctx:i}),s.$set(c)},i(i){o||(m(t.$$.fragment,i),m(s.$$.fragment,i),o=!0)},o(i){g(t.$$.fragment,i),g(s.$$.fragment,i),o=!1},d(i){i&&v(e),C(t),C(s)}}}function Zp(r){let e,t;return e=new Ao({props:{show:r[0],title:"Create New Rule",ariaLabelledBy:"dialog-title",preventBackdropClose:r[4],preventEscapeClose:r[4],$$slots:{footer:[Fp],body:[Lp]},$$scope:{ctx:r}}}),e.$on("cancel",r[6]),e.$on("keydown",r[7]),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,[s]){const o={};1&s&&(o.show=n[0]),16&s&&(o.preventBackdropClose=n[4]),16&s&&(o.preventEscapeClose=n[4]),4126&s&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Up(r,e,t){const n=Qn();let s,{show:o=!1}=e,{errorMessage:i=""}=e,l="",a=!1;function c(){l.trim()&&!a&&(t(4,a=!0),n("create",l.trim()))}function d(){a||(n("cancel"),t(3,l=""))}return r.$$set=u=>{"show"in u&&t(0,o=u.show),"errorMessage"in u&&t(1,i=u.errorMessage)},r.$$.update=()=>{5&r.$$.dirty&&o&&s&&setTimeout(()=>s==null?void 0:s.focus(),100),3&r.$$.dirty&&(o&&!i||t(4,a=!1)),3&r.$$.dirty&&(o||i||t(3,l=""))},[o,i,s,l,a,c,d,function(u){a||u.detail.key==="Enter"&&(u.detail.preventDefault(),c())},function(u){a||(u.key==="Enter"?(u.preventDefault(),c()):u.key==="Escape"&&(u.preventDefault(),d()))},function(u){l=u,t(3,l),t(0,o),t(1,i)},function(u){s=u,t(2,s)}]}class Dp extends ae{constructor(e){super(),le(this,e,Up,Zp,oe,{show:0,errorMessage:1})}}function Yr(r,e,t){const n=r.slice();return n[18]=e[t],n}function jp(r){let e,t,n,s,o,i,l,a,c;function d($){r[15]($)}function u($){r[16]($)}e=new Q({props:{size:2,color:"secondary",$$slots:{default:[qp]},$$scope:{ctx:r}}});let f={triggerOn:r[1].length===0?[]:void 0,$$slots:{default:[Wp]},$$scope:{ctx:r}};r[7]!==void 0&&(f.requestClose=r[7]),r[6]!==void 0&&(f.focusedIndex=r[6]),n=new xe.Root({props:f}),be.push(()=>ke(n,"requestClose",d)),be.push(()=>ke(n,"focusedIndex",u));let p=r[3]&&eo(r),h=r[4]&&to(r);return{c(){S(e.$$.fragment),t=N(),S(n.$$.fragment),i=N(),p&&p.c(),l=N(),h&&h.c(),a=ve()},m($,b){x(e,$,b),y($,t,b),x(n,$,b),y($,i,b),p&&p.m($,b),y($,l,b),h&&h.m($,b),y($,a,b),c=!0},p($,b){const E={};2097152&b&&(E.$$scope={dirty:b,ctx:$}),e.$set(E);const I={};2&b&&(I.triggerOn=$[1].length===0?[]:void 0),2097442&b&&(I.$$scope={dirty:b,ctx:$}),!s&&128&b&&(s=!0,I.requestClose=$[7],Me(()=>s=!1)),!o&&64&b&&(o=!0,I.focusedIndex=$[6],Me(()=>o=!1)),n.$set(I),$[3]?p?(p.p($,b),8&b&&m(p,1)):(p=eo($),p.c(),m(p,1),p.m(l.parentNode,l)):p&&(j(),g(p,1,1,()=>{p=null}),V()),$[4]?h?(h.p($,b),16&b&&m(h,1)):(h=to($),h.c(),m(h,1),h.m(a.parentNode,a)):h&&(j(),g(h,1,1,()=>{h=null}),V())},i($){c||(m(e.$$.fragment,$),m(n.$$.fragment,$),m(p),m(h),c=!0)},o($){g(e.$$.fragment,$),g(n.$$.fragment,$),g(p),g(h),c=!1},d($){$&&(v(t),v(i),v(l),v(a)),C(e,$),C(n,$),p&&p.d($),h&&h.d($)}}}function Vp(r){let e;return{c(){e=k("input"),w(e,"type","text"),e.value="No existing rules found",e.readOnly=!0,w(e,"class","c-dropdown-input svelte-z1s6x7")},m(t,n){y(t,e,n)},p:D,i:D,o:D,d(t){t&&v(e)}}}function qp(r){let e;return{c(){e=R("Select existing rules to auto import to .augment/rules")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function Hp(r){let e,t,n,s,o,i;return o=new rs({props:{class:"c-dropdown-chevron"}}),{c(){e=k("div"),t=k("input"),s=N(),S(o.$$.fragment),w(t,"type","text"),t.value=n=r[5]?r[5].label:"Existing rules",t.readOnly=!0,w(t,"class","c-dropdown-input svelte-z1s6x7"),w(e,"class","c-dropdown-trigger svelte-z1s6x7")},m(l,a){y(l,e,a),_(e,t),_(e,s),x(o,e,null),i=!0},p(l,a){(!i||32&a&&n!==(n=l[5]?l[5].label:"Existing rules")&&t.value!==n)&&(t.value=n)},i(l){i||(m(o.$$.fragment,l),i=!0)},o(l){g(o.$$.fragment,l),i=!1},d(l){l&&v(e),C(o)}}}function Bp(r){let e,t=r[18].label+"";return{c(){e=R(t)},m(n,s){y(n,e,s)},p(n,s){2&s&&t!==(t=n[18].label+"")&&de(e,t)},d(n){n&&v(e)}}}function Xr(r){var s;let e,t;function n(){return r[14](r[18])}return e=new xe.Item({props:{onSelect:n,highlight:((s=r[5])==null?void 0:s.label)===r[18].label,$$slots:{default:[Bp]},$$scope:{ctx:r}}}),{c(){S(e.$$.fragment)},m(o,i){x(e,o,i),t=!0},p(o,i){var a;r=o;const l={};2&i&&(l.onSelect=n),34&i&&(l.highlight=((a=r[5])==null?void 0:a.label)===r[18].label),2097154&i&&(l.$$scope={dirty:i,ctx:r}),e.$set(l)},i(o){t||(m(e.$$.fragment,o),t=!0)},o(o){g(e.$$.fragment,o),t=!1},d(o){C(e,o)}}}function Qr(r){let e,t,n,s;return e=new xe.Separator({}),n=new xe.Label({props:{$$slots:{default:[Gp]},$$scope:{ctx:r}}}),{c(){S(e.$$.fragment),t=N(),S(n.$$.fragment)},m(o,i){x(e,o,i),y(o,t,i),x(n,o,i),s=!0},p(o,i){const l={};2097442&i&&(l.$$scope={dirty:i,ctx:o}),n.$set(l)},i(o){s||(m(e.$$.fragment,o),m(n.$$.fragment,o),s=!0)},o(o){g(e.$$.fragment,o),g(n.$$.fragment,o),s=!1},d(o){o&&v(t),C(e,o),C(n,o)}}}function Gp(r){var n;let e,t=(r[8]!==void 0?r[1][r[8]].description:(n=r[5])==null?void 0:n.description)+"";return{c(){e=R(t)},m(s,o){y(s,e,o)},p(s,o){var i;290&o&&t!==(t=(s[8]!==void 0?s[1][s[8]].description:(i=s[5])==null?void 0:i.description)+"")&&de(e,t)},d(s){s&&v(e)}}}function Jp(r){let e,t,n,s=ce(r[1]),o=[];for(let a=0;a<s.length;a+=1)o[a]=Xr(Yr(r,s,a));const i=a=>g(o[a],1,1,()=>{o[a]=null});let l=(r[8]!==void 0||r[5])&&Qr(r);return{c(){for(let a=0;a<o.length;a+=1)o[a].c();e=N(),l&&l.c(),t=ve()},m(a,c){for(let d=0;d<o.length;d+=1)o[d]&&o[d].m(a,c);y(a,e,c),l&&l.m(a,c),y(a,t,c),n=!0},p(a,c){if(546&c){let d;for(s=ce(a[1]),d=0;d<s.length;d+=1){const u=Yr(a,s,d);o[d]?(o[d].p(u,c),m(o[d],1)):(o[d]=Xr(u),o[d].c(),m(o[d],1),o[d].m(e.parentNode,e))}for(j(),d=s.length;d<o.length;d+=1)i(d);V()}a[8]!==void 0||a[5]?l?(l.p(a,c),288&c&&m(l,1)):(l=Qr(a),l.c(),m(l,1),l.m(t.parentNode,t)):l&&(j(),g(l,1,1,()=>{l=null}),V())},i(a){if(!n){for(let c=0;c<s.length;c+=1)m(o[c]);m(l),n=!0}},o(a){o=o.filter(Boolean);for(let c=0;c<o.length;c+=1)g(o[c]);g(l),n=!1},d(a){a&&(v(e),v(t)),yt(o,a),l&&l.d(a)}}}function Wp(r){let e,t,n,s;return e=new xe.Trigger({props:{$$slots:{default:[Hp]},$$scope:{ctx:r}}}),n=new xe.Content({props:{align:"start",side:"bottom",$$slots:{default:[Jp]},$$scope:{ctx:r}}}),{c(){S(e.$$.fragment),t=N(),S(n.$$.fragment)},m(o,i){x(e,o,i),y(o,t,i),x(n,o,i),s=!0},p(o,i){const l={};2097184&i&&(l.$$scope={dirty:i,ctx:o}),e.$set(l);const a={};2097442&i&&(a.$$scope={dirty:i,ctx:o}),n.$set(a)},i(o){s||(m(e.$$.fragment,o),m(n.$$.fragment,o),s=!0)},o(o){g(e.$$.fragment,o),g(n.$$.fragment,o),s=!1},d(o){o&&v(t),C(e,o),C(n,o)}}}function eo(r){let e,t;return e=new In({props:{variant:"soft",color:"error",size:1,$$slots:{icon:[Yp],default:[Kp]},$$scope:{ctx:r}}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,s){const o={};2097160&s&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Kp(r){let e;return{c(){e=R(r[3])},m(t,n){y(t,e,n)},p(t,n){8&n&&de(e,t[3])},d(t){t&&v(e)}}}function Yp(r){let e,t;return e=new ss({props:{slot:"icon"}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p:D,i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function to(r){let e,t;return e=new In({props:{variant:"soft",color:"success",size:1,$$slots:{icon:[Qp],default:[Xp]},$$scope:{ctx:r}}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,s){const o={};2097168&s&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Xp(r){let e;return{c(){e=R(r[4])},m(t,n){y(t,e,n)},p(t,n){16&n&&de(e,t[4])},d(t){t&&v(e)}}}function Qp(r){let e,t;return e=new mi({props:{slot:"icon"}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p:D,i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function e1(r){let e,t,n,s;const o=[Vp,jp],i=[];function l(a,c){return a[1].length===0?0:1}return t=l(r),n=i[t]=o[t](r),{c(){e=k("div"),n.c(),w(e,"slot","body"),w(e,"class","c-auto-import-rules-dialog svelte-z1s6x7")},m(a,c){y(a,e,c),i[t].m(e,null),s=!0},p(a,c){let d=t;t=l(a),t===d?i[t].p(a,c):(j(),g(i[d],1,1,()=>{i[d]=null}),V(),n=i[t],n?n.p(a,c):(n=i[t]=o[t](a),n.c()),m(n,1),n.m(e,null))},i(a){s||(m(n),s=!0)},o(a){g(n),s=!1},d(a){a&&v(e),i[t].d()}}}function t1(r){let e;return{c(){e=R("Cancel")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function no(r){let e,t;return e=new we({props:{color:"accent",variant:"solid",disabled:!r[5]||r[2],loading:r[2],$$slots:{default:[n1]},$$scope:{ctx:r}}}),e.$on("click",r[10]),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,s){const o={};36&s&&(o.disabled=!n[5]||n[2]),4&s&&(o.loading=n[2]),2097156&s&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function n1(r){let e,t=r[2]?"Importing...":"Import ";return{c(){e=R(t)},m(n,s){y(n,e,s)},p(n,s){4&s&&t!==(t=n[2]?"Importing...":"Import ")&&de(e,t)},d(n){n&&v(e)}}}function s1(r){let e,t,n,s;t=new we({props:{variant:"solid",color:"neutral",disabled:r[2],$$slots:{default:[t1]},$$scope:{ctx:r}}}),t.$on("click",r[11]);let o=r[1].length>0&&no(r);return{c(){e=k("div"),S(t.$$.fragment),n=N(),o&&o.c(),w(e,"slot","footer")},m(i,l){y(i,e,l),x(t,e,null),_(e,n),o&&o.m(e,null),s=!0},p(i,l){const a={};4&l&&(a.disabled=i[2]),2097152&l&&(a.$$scope={dirty:l,ctx:i}),t.$set(a),i[1].length>0?o?(o.p(i,l),2&l&&m(o,1)):(o=no(i),o.c(),m(o,1),o.m(e,null)):o&&(j(),g(o,1,1,()=>{o=null}),V())},i(i){s||(m(t.$$.fragment,i),m(o),s=!0)},o(i){g(t.$$.fragment,i),g(o),s=!1},d(i){i&&v(e),C(t),o&&o.d()}}}function r1(r){let e,t,n,s;return e=new Ao({props:{show:r[0],title:"Auto Import Rules",ariaLabelledBy:"dialog-title",preventBackdropClose:r[2],preventEscapeClose:r[2],$$slots:{footer:[s1],body:[e1]},$$scope:{ctx:r}}}),e.$on("cancel",r[11]),{c(){S(e.$$.fragment)},m(o,i){x(e,o,i),t=!0,n||(s=Ie(window,"keydown",r[12]),n=!0)},p(o,[i]){const l={};1&i&&(l.show=o[0]),4&i&&(l.preventBackdropClose=o[2]),4&i&&(l.preventEscapeClose=o[2]),2097662&i&&(l.$$scope={dirty:i,ctx:o}),e.$set(l)},i(o){t||(m(e.$$.fragment,o),t=!0)},o(o){g(e.$$.fragment,o),t=!1},d(o){C(e,o),n=!1,s()}}}function o1(r,e,t){let n,s,o=D,i=()=>(o(),o=an(a,A=>t(8,s=A)),a);r.$$.on_destroy.push(()=>o());const l=Qn();let a,{show:c=!1}=e,{options:d=[]}=e,{isLoading:u=!1}=e,{errorMessage:f=""}=e,{successMessage:p=""}=e,h=n;i();let $=()=>{};function b(A){t(5,h=A),$()}function E(){h&&!u&&l("select",h)}function I(){u||(l("cancel"),t(5,h=n))}return r.$$set=A=>{"show"in A&&t(0,c=A.show),"options"in A&&t(1,d=A.options),"isLoading"in A&&t(2,u=A.isLoading),"errorMessage"in A&&t(3,f=A.errorMessage),"successMessage"in A&&t(4,p=A.successMessage)},r.$$.update=()=>{2&r.$$.dirty&&t(13,n=d.length>0?d[0]:null),8193&r.$$.dirty&&c&&t(5,h=n)},[c,d,u,f,p,h,a,$,s,b,E,I,function(A){c&&!u&&(A.key==="Escape"?(A.preventDefault(),I()):A.key==="Enter"&&h&&(A.preventDefault(),E()))},n,A=>b(A),function(A){$=A,t(7,$)},function(A){a=A,i(t(6,a))}]}class i1 extends ae{constructor(e){super(),le(this,e,o1,r1,oe,{show:0,options:1,isLoading:2,errorMessage:3,successMessage:4})}}function so(r,e,t){const n=r.slice();return n[35]=e[t],n}function ro(r,e,t){const n=r.slice();return n[38]=e[t],n}function a1(r){let e;return{c(){e=R("Rules")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function l1(r){let e;return{c(){e=R("Learn more")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function c1(r){let e,t,n=[],s=new Map,o=ce(r[10]);const i=l=>l[38].path;for(let l=0;l<o.length;l+=1){let a=ro(r,o,l),c=i(a);s.set(c,n[l]=oo(c,a))}return{c(){for(let l=0;l<n.length;l+=1)n[l].c();e=ve()},m(l,a){for(let c=0;c<n.length;c+=1)n[c]&&n[c].m(l,a);y(l,e,a),t=!0},p(l,a){99328&a[0]&&(o=ce(l[10]),j(),n=Ke(n,a,i,1,l,o,s,e.parentNode,Ye,oo,e,ro),V())},i(l){if(!t){for(let a=0;a<o.length;a+=1)m(n[a]);t=!0}},o(l){for(let a=0;a<n.length;a+=1)g(n[a]);t=!1},d(l){l&&v(e);for(let a=0;a<n.length;a+=1)n[a].d(l)}}}function d1(r){let e,t,n;return t=new Q({props:{size:1,color:"neutral",$$slots:{default:[y1]},$$scope:{ctx:r}}}),{c(){e=k("div"),S(t.$$.fragment),w(e,"class","c-rules-list-empty svelte-5krsve")},m(s,o){y(s,e,o),x(t,e,null),n=!0},p(s,o){const i={};1024&o[1]&&(i.$$scope={dirty:o,ctx:s}),t.$set(i)},i(s){n||(m(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&v(e),C(t)}}}function u1(r){let e,t;return e=new $i({}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function p1(r){let e,t;return e=new rt({props:{content:"No description found",$$slots:{default:[m1]},$$scope:{ctx:r}}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function m1(r){let e,t;return e=new ss({}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function f1(r){let e,t=r[38].path+"";return{c(){e=R(t)},m(n,s){y(n,e,s)},p(n,s){1024&s[0]&&t!==(t=n[38].path+"")&&de(e,t)},d(n){n&&v(e)}}}function g1(r){let e,t,n,s,o,i,l,a,c;const d=[p1,u1],u=[];function f(p,h){return p[38].type!==ii.AGENT_REQUESTED||p[38].description?1:0}return n=f(r),s=u[n]=d[n](r),l=new Q({props:{size:1,color:"neutral",$$slots:{default:[f1]},$$scope:{ctx:r}}}),{c(){e=k("div"),t=k("div"),s.c(),o=N(),i=k("div"),S(l.$$.fragment),a=N(),w(t,"class","l-icon-wrapper svelte-5krsve"),w(i,"class","c-rule-item-path svelte-5krsve"),w(e,"class","c-rule-item-info svelte-5krsve"),w(e,"slot","header-left")},m(p,h){y(p,e,h),_(e,t),u[n].m(t,null),_(e,o),_(e,i),x(l,i,null),_(e,a),c=!0},p(p,h){let $=n;n=f(p),n!==$&&(j(),g(u[$],1,1,()=>{u[$]=null}),V(),s=u[n],s||(s=u[n]=d[n](p),s.c()),m(s,1),s.m(t,null));const b={};1024&h[0]|1024&h[1]&&(b.$$scope={dirty:h,ctx:p}),l.$set(b)},i(p){c||(m(s),m(l.$$.fragment,p),c=!0)},o(p){g(s),g(l.$$.fragment,p),c=!1},d(p){p&&v(e),u[n].d(),C(l)}}}function $1(r){let e,t;return e=new fi({props:{slot:"iconRight"}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p:D,i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function h1(r){let e,t;return e=new gi({props:{slot:"iconRight"}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p:D,i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function v1(r){let e,t,n,s,o,i,l,a,c,d;function u(...f){return r[26](r[38],...f)}return s=new Oi({props:{rule:r[38],onSave:u}}),i=new we({props:{size:1,variant:"ghost-block",color:"neutral",class:"c-rule-item-button",$$slots:{iconRight:[$1]},$$scope:{ctx:r}}}),i.$on("click",function(...f){return r[27](r[38],...f)}),a=new we({props:{size:1,variant:"ghost-block",color:"neutral",class:"c-rule-item-button",$$slots:{iconRight:[h1]},$$scope:{ctx:r}}}),a.$on("click",function(...f){return r[28](r[38],...f)}),{c(){e=k("div"),t=k("div"),n=k("div"),S(s.$$.fragment),o=N(),S(i.$$.fragment),l=N(),S(a.$$.fragment),c=N(),w(n,"class","c-rules-dropdown svelte-5krsve"),w(t,"class","status-controls svelte-5krsve"),w(e,"class","server-actions"),w(e,"slot","header-right")},m(f,p){y(f,e,p),_(e,t),_(t,n),x(s,n,null),_(t,o),x(i,t,null),_(t,l),x(a,t,null),_(e,c),d=!0},p(f,p){r=f;const h={};1024&p[0]&&(h.rule=r[38]),1024&p[0]&&(h.onSave=u),s.$set(h);const $={};1024&p[1]&&($.$$scope={dirty:p,ctx:r}),i.$set($);const b={};1024&p[1]&&(b.$$scope={dirty:p,ctx:r}),a.$set(b)},i(f){d||(m(s.$$.fragment,f),m(i.$$.fragment,f),m(a.$$.fragment,f),d=!0)},o(f){g(s.$$.fragment,f),g(i.$$.fragment,f),g(a.$$.fragment,f),d=!1},d(f){f&&v(e),C(s),C(i),C(a)}}}function oo(r,e){let t,n,s;return n=new Do({props:{isClickable:!0,$$slots:{"header-right":[v1],"header-left":[g1]},$$scope:{ctx:e}}}),n.$on("click",function(){return e[29](e[38])}),{key:r,first:null,c(){t=ve(),S(n.$$.fragment),this.first=t},m(o,i){y(o,t,i),x(n,o,i),s=!0},p(o,i){e=o;const l={};1024&i[0]|1024&i[1]&&(l.$$scope={dirty:i,ctx:e}),n.$set(l)},i(o){s||(m(n.$$.fragment,o),s=!0)},o(o){g(n.$$.fragment,o),s=!1},d(o){o&&v(t),C(n,o)}}}function y1(r){let e;return{c(){e=R("No rules files found")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function w1(r){let e,t,n,s,o;return t=new ln({}),{c(){e=k("div"),S(t.$$.fragment),n=N(),s=k("span"),s.textContent="Create new rule file",w(e,"class","c-rules-actions-button-content svelte-5krsve")},m(i,l){y(i,e,l),x(t,e,null),_(e,n),_(e,s),o=!0},p:D,i(i){o||(m(t.$$.fragment,i),o=!0)},o(i){g(t.$$.fragment,i),o=!1},d(i){i&&v(e),C(t)}}}function S1(r){let e,t,n,s,o,i,l;return t=new fo({}),i=new rs({}),{c(){e=k("div"),S(t.$$.fragment),n=N(),s=k("span"),s.textContent="Import rules",o=N(),S(i.$$.fragment),w(e,"class","c-rules-actions-button-content svelte-5krsve")},m(a,c){y(a,e,c),x(t,e,null),_(e,n),_(e,s),_(e,o),x(i,e,null),l=!0},p:D,i(a){l||(m(t.$$.fragment,a),m(i.$$.fragment,a),l=!0)},o(a){g(t.$$.fragment,a),g(i.$$.fragment,a),l=!1},d(a){a&&v(e),C(t),C(i)}}}function x1(r){let e,t;return e=new we({props:{size:1,variant:"soft",color:"neutral",class:"c-rules-action-button",$$slots:{default:[S1]},$$scope:{ctx:r}}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,s){const o={};1024&s[1]&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function C1(r){let e,t=r[35].label+"";return{c(){e=R(t)},m(n,s){y(n,e,s)},p:D,d(n){n&&v(e)}}}function io(r,e){let t,n,s;return n=new xe.Item({props:{onSelect:function(){return e[31](e[35])},$$slots:{default:[C1]},$$scope:{ctx:e}}}),{key:r,first:null,c(){t=ve(),S(n.$$.fragment),this.first=t},m(o,i){y(o,t,i),x(n,o,i),s=!0},p(o,i){e=o;const l={};1024&i[1]&&(l.$$scope={dirty:i,ctx:e}),n.$set(l)},i(o){s||(m(n.$$.fragment,o),s=!0)},o(o){g(n.$$.fragment,o),s=!1},d(o){o&&v(t),C(n,o)}}}function ao(r){let e,t,n,s;return e=new xe.Separator({}),n=new xe.Label({props:{$$slots:{default:[_1]},$$scope:{ctx:r}}}),{c(){S(e.$$.fragment),t=N(),S(n.$$.fragment)},m(o,i){x(e,o,i),y(o,t,i),x(n,o,i),s=!0},p(o,i){const l={};2048&i[0]|1024&i[1]&&(l.$$scope={dirty:i,ctx:o}),n.$set(l)},i(o){s||(m(e.$$.fragment,o),m(n.$$.fragment,o),s=!0)},o(o){g(e.$$.fragment,o),g(n.$$.fragment,o),s=!1},d(o){o&&v(t),C(e,o),C(n,o)}}}function _1(r){let e,t=(r[11]!==void 0?r[20][r[11]].description:r[20][0])+"";return{c(){e=R(t)},m(n,s){y(n,e,s)},p(n,s){2048&s[0]&&t!==(t=(n[11]!==void 0?n[20][n[11]].description:n[20][0])+"")&&de(e,t)},d(n){n&&v(e)}}}function b1(r){let e,t,n,s=[],o=new Map,i=ce(r[20]);const l=c=>c[35].id;for(let c=0;c<i.length;c+=1){let d=so(r,i,c),u=l(d);o.set(u,s[c]=io(u,d))}let a=r[11]!==void 0&&ao(r);return{c(){for(let c=0;c<s.length;c+=1)s[c].c();e=N(),a&&a.c(),t=ve()},m(c,d){for(let u=0;u<s.length;u+=1)s[u]&&s[u].m(c,d);y(c,e,d),a&&a.m(c,d),y(c,t,d),n=!0},p(c,d){3145728&d[0]&&(i=ce(c[20]),j(),s=Ke(s,d,l,1,c,i,o,e.parentNode,Ye,io,e,so),V()),c[11]!==void 0?a?(a.p(c,d),2048&d[0]&&m(a,1)):(a=ao(c),a.c(),m(a,1),a.m(t.parentNode,t)):a&&(j(),g(a,1,1,()=>{a=null}),V())},i(c){if(!n){for(let d=0;d<i.length;d+=1)m(s[d]);m(a),n=!0}},o(c){for(let d=0;d<s.length;d+=1)g(s[d]);g(a),n=!1},d(c){c&&(v(e),v(t));for(let d=0;d<s.length;d+=1)s[d].d(c);a&&a.d(c)}}}function k1(r){let e,t,n,s;return e=new xe.Trigger({props:{$$slots:{default:[x1]},$$scope:{ctx:r}}}),n=new xe.Content({props:{align:"start",side:"bottom",$$slots:{default:[b1]},$$scope:{ctx:r}}}),{c(){S(e.$$.fragment),t=N(),S(n.$$.fragment)},m(o,i){x(e,o,i),y(o,t,i),x(n,o,i),s=!0},p(o,i){const l={};1024&i[1]&&(l.$$scope={dirty:i,ctx:o}),e.$set(l);const a={};2048&i[0]|1024&i[1]&&(a.$$scope={dirty:i,ctx:o}),n.$set(a)},i(o){s||(m(e.$$.fragment,o),m(n.$$.fragment,o),s=!0)},o(o){g(e.$$.fragment,o),g(n.$$.fragment,o),s=!1},d(o){o&&v(t),C(e,o),C(n,o)}}}function M1(r){let e;return{c(){e=R("User Guidelines")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function A1(r){let e;return{c(){e=R("Learn more")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function N1(r){let e,t,n,s,o,i,l,a,c,d,u,f,p,h,$,b,E,I,A,M,T,O,G,ne,z,Z,P,H,ie,F,J,U,fe,De,Nt,Ut;n=new Q({props:{class:"c-section-header",size:3,color:"primary",$$slots:{default:[a1]},$$scope:{ctx:r}}}),a=new Q({props:{size:1,weight:"regular",$$slots:{default:[l1]},$$scope:{ctx:r}}});const St=[d1,c1],it=[];function ls(Y,pe){return Y[10].length===0?0:1}function Wo(Y){r[32](Y)}function Ko(Y){r[33](Y)}u=ls(r),f=it[u]=St[u](r),$=new we({props:{size:1,variant:"soft",color:"neutral",class:"c-rules-action-button",$$slots:{default:[w1]},$$scope:{ctx:r}}}),$.$on("click",r[30]);let On={$$slots:{default:[k1]},$$scope:{ctx:r}};return r[9]!==void 0&&(On.requestClose=r[9]),r[8]!==void 0&&(On.focusedIndex=r[8]),E=new xe.Root({props:On}),be.push(()=>ke(E,"requestClose",Wo)),be.push(()=>ke(E,"focusedIndex",Ko)),O=new Q({props:{class:"c-section-header",size:3,color:"primary",$$slots:{default:[M1]},$$scope:{ctx:r}}}),P=new Q({props:{size:1,weight:"regular",$$slots:{default:[A1]},$$scope:{ctx:r}}}),ie=new Jo({props:{userGuidelines:r[0],userGuidelinesLengthLimit:r[1],updateUserGuideline:r[2]}}),J=new Dp({props:{show:r[12],errorMessage:r[13]}}),J.$on("create",r[24]),J.$on("cancel",r[25]),fe=new i1({props:{show:r[3],options:r[4],isLoading:r[5],errorMessage:r[6],successMessage:r[7]}}),fe.$on("select",r[22]),fe.$on("cancel",r[23]),{c(){e=k("div"),t=k("div"),S(n.$$.fragment),s=N(),o=k("div"),i=R(`Rules are instructions for Augment Chat and Agent that can be applied automatically across all
      conversations or referenced in specific conversations using @mentions (e.g., @rule-file.md) `),l=k("a"),S(a.$$.fragment),c=N(),d=k("div"),f.c(),p=N(),h=k("div"),S($.$$.fragment),b=N(),S(E.$$.fragment),M=N(),T=k("div"),S(O.$$.fragment),G=N(),ne=k("div"),z=R(`User Guidelines allow you to control Augment's behavior through natural language instructions.
      These guidelines are applied globally to all Chat and Agent interactions. `),Z=k("a"),S(P.$$.fragment),H=N(),S(ie.$$.fragment),F=N(),S(J.$$.fragment),U=N(),S(fe.$$.fragment),w(l,"href","https://docs.augmentcode.com/setup-augment/guidelines#workspace-guidelines"),w(l,"target","_blank"),w(d,"class","c-rules-list svelte-5krsve"),w(h,"class","c-rules-actions-container svelte-5krsve"),w(t,"class","c-rules-section svelte-5krsve"),w(Z,"href","https://docs.augmentcode.com/setup-augment/guidelines#workspace-guidelines"),w(Z,"target","_blank"),w(T,"class","c-user-guidelines-section svelte-5krsve"),w(e,"class","c-rules-category svelte-5krsve")},m(Y,pe){y(Y,e,pe),_(e,t),x(n,t,null),_(t,s),_(t,o),_(o,i),_(o,l),x(a,l,null),_(t,c),_(t,d),it[u].m(d,null),_(t,p),_(t,h),x($,h,null),_(h,b),x(E,h,null),_(e,M),_(e,T),x(O,T,null),_(T,G),_(T,ne),_(ne,z),_(ne,Z),x(P,Z,null),_(T,H),x(ie,T,null),y(Y,F,pe),x(J,Y,pe),y(Y,U,pe),x(fe,Y,pe),De=!0,Nt||(Ut=Ie(window,"message",r[14].onMessageFromExtension),Nt=!0)},p(Y,pe){const cs={};1024&pe[1]&&(cs.$$scope={dirty:pe,ctx:Y}),n.$set(cs);const ds={};1024&pe[1]&&(ds.$$scope={dirty:pe,ctx:Y}),a.$set(ds);let zn=u;u=ls(Y),u===zn?it[u].p(Y,pe):(j(),g(it[zn],1,1,()=>{it[zn]=null}),V(),f=it[u],f?f.p(Y,pe):(f=it[u]=St[u](Y),f.c()),m(f,1),f.m(d,null));const us={};1024&pe[1]&&(us.$$scope={dirty:pe,ctx:Y}),$.$set(us);const dn={};2048&pe[0]|1024&pe[1]&&(dn.$$scope={dirty:pe,ctx:Y}),!I&&512&pe[0]&&(I=!0,dn.requestClose=Y[9],Me(()=>I=!1)),!A&&256&pe[0]&&(A=!0,dn.focusedIndex=Y[8],Me(()=>A=!1)),E.$set(dn);const ps={};1024&pe[1]&&(ps.$$scope={dirty:pe,ctx:Y}),O.$set(ps);const ms={};1024&pe[1]&&(ms.$$scope={dirty:pe,ctx:Y}),P.$set(ms);const un={};1&pe[0]&&(un.userGuidelines=Y[0]),2&pe[0]&&(un.userGuidelinesLengthLimit=Y[1]),4&pe[0]&&(un.updateUserGuideline=Y[2]),ie.$set(un);const Fn={};4096&pe[0]&&(Fn.show=Y[12]),8192&pe[0]&&(Fn.errorMessage=Y[13]),J.$set(Fn);const Tt={};8&pe[0]&&(Tt.show=Y[3]),16&pe[0]&&(Tt.options=Y[4]),32&pe[0]&&(Tt.isLoading=Y[5]),64&pe[0]&&(Tt.errorMessage=Y[6]),128&pe[0]&&(Tt.successMessage=Y[7]),fe.$set(Tt)},i(Y){De||(m(n.$$.fragment,Y),m(a.$$.fragment,Y),m(f),m($.$$.fragment,Y),m(E.$$.fragment,Y),m(O.$$.fragment,Y),m(P.$$.fragment,Y),m(ie.$$.fragment,Y),m(J.$$.fragment,Y),m(fe.$$.fragment,Y),De=!0)},o(Y){g(n.$$.fragment,Y),g(a.$$.fragment,Y),g(f),g($.$$.fragment,Y),g(E.$$.fragment,Y),g(O.$$.fragment,Y),g(P.$$.fragment,Y),g(ie.$$.fragment,Y),g(J.$$.fragment,Y),g(fe.$$.fragment,Y),De=!1},d(Y){Y&&(v(e),v(F),v(U)),C(n),C(a),it[u].d(),C($),C(E),C(O),C(P),C(ie),C(J,Y),C(fe,Y),Nt=!1,Ut()}}}function T1(r,e,t){let n,s,o,i,l=D,a=()=>(l(),l=an(G,Z=>t(11,s=Z)),G);r.$$.on_destroy.push(()=>l());let{userGuidelines:c=""}=e,{userGuidelinesLengthLimit:d}=e,{updateUserGuideline:u=()=>!1}=e;const f=new go(Ne),p=new Ri(f),h=new Ep(Ne,f,p);f.registerConsumer(p);const $=p.getRulesFiles();Pe(r,$,Z=>t(10,n=Z));const b=h.getShowCreateRuleDialog();Pe(r,b,Z=>t(12,o=Z));const E=h.getCreateRuleError();Pe(r,E,Z=>t(13,i=Z));let I=!1,A=[],M=!1,T="",O="",G;a();let ne=()=>{};async function z(Z){try{Z.id==="select_file_or_directory"?await h.selectFileToImport():Z.id==="auto_import"&&await async function(){try{t(6,T=""),t(7,O="");const P=await h.getAutoImportOptions();t(4,A=P.data.options),t(3,I=!0)}catch(P){console.error("Failed to get auto-import options:",P),t(6,T="Failed to detect existing rules in workspace.")}}()}catch(P){console.error("Failed to handle import select:",P)}ne&&ne()}return uo(()=>{p.requestRules()}),r.$$set=Z=>{"userGuidelines"in Z&&t(0,c=Z.userGuidelines),"userGuidelinesLengthLimit"in Z&&t(1,d=Z.userGuidelinesLengthLimit),"updateUserGuideline"in Z&&t(2,u=Z.updateUserGuideline)},[c,d,u,I,A,M,T,O,G,ne,n,s,o,i,f,p,h,$,b,E,[{label:"Auto import existing rules in the workspace",id:"auto_import",description:"Choose existing rules in your workspace to auto import to Augment."},{label:"Select file(s) or directory to import",id:"select_file_or_directory",description:"Select an existing directory or list of markdown files to import to Augment."}],z,async function(Z){const P=Z.detail;try{t(5,M=!0),t(6,T="");const H=await h.processAutoImportSelection(P);let ie=`Successfully imported ${H.importedRulesCount} rule${H.importedRulesCount!==1?"s":""} from ${P.label}`;H.duplicatesCount>0&&(ie+=`, ${H.duplicatesCount} duplicate${H.duplicatesCount!==1?"s":""} skipped`),H.totalAttempted>H.importedRulesCount+H.duplicatesCount&&(ie+=`, ${H.totalAttempted-H.importedRulesCount-H.duplicatesCount} failed`),t(7,O=ie),setTimeout(()=>{t(3,I=!1),t(7,O="")},500)}catch(H){console.error("Failed to process auto-import selection:",H),t(6,T="Failed to import rules. Please try again.")}finally{t(5,M=!1)}},function(){t(3,I=!1),t(6,T=""),t(7,O="")},function(Z){h.handleCreateRuleWithName(Z.detail)},function(){h.hideCreateRuleDialog()},async(Z,P,H)=>{await p.updateRuleContent({type:P,path:Z.path,content:Z.content,description:H})},(Z,P)=>{P.stopPropagation(),h.openRule(Z.path)},(Z,P)=>{P.stopPropagation(),h.deleteRule(Z.path)},Z=>h.openRule(Z.path),()=>h.createRule(),Z=>z(Z),function(Z){ne=Z,t(9,ne)},function(Z){G=Z,a(t(8,G))}]}class E1 extends ae{constructor(e){super(),le(this,e,T1,N1,oe,{userGuidelines:0,userGuidelinesLengthLimit:1,updateUserGuideline:2},null,[-1,-1])}}function I1(r){let e,t,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},r[0]],s={};for(let o=0;o<n.length;o+=1)s=Ae(s,n[o]);return{c(){e=_e("svg"),t=new Mn(!0),this.h()},l(o){e=An(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=Nn(e);t=Tn(i,!0),i.forEach(v),this.h()},h(){t.a=null,ft(e,s)},m(o,i){En(o,e,i),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M352 146.2 462 256 352 365.8V312c0-13.3-10.7-24-24-24H208v-64h120c13.3 0 24-10.7 24-24zM512 256c0-11.5-4.6-22.5-12.7-30.6L383.2 109.6c-8.7-8.7-20.5-13.6-32.8-13.6-25.6 0-46.4 20.8-46.4 46.4V176h-96c-26.5 0-48 21.5-48 48v64c0 26.5 21.5 48 48 48h96v33.6c0 25.6 20.8 46.4 46.4 46.4 12.3 0 24.1-4.9 32.8-13.6l116.1-115.8c8.1-8.1 12.7-19.1 12.7-30.6M168 80c13.3 0 24-10.7 24-24s-10.7-24-24-24H88C39.4 32 0 71.4 0 120v272c0 48.6 39.4 88 88 88h80c13.3 0 24-10.7 24-24s-10.7-24-24-24H88c-22.1 0-40-17.9-40-40V120c0-22.1 17.9-40 40-40z"/>',e)},p(o,[i]){ft(e,s=pt(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&i&&o[0]]))},i:D,o:D,d(o){o&&v(e)}}}function R1(r,e,t){return r.$$set=n=>{t(0,e=Ae(Ae({},e),We(n)))},[e=We(e)]}class P1 extends ae{constructor(e){super(),le(this,e,R1,I1,oe,{})}}function L1(r){let e;return{c(){e=R("Sign Out")},m(t,n){y(t,e,n)},d(t){t&&v(e)}}}function O1(r){let e,t;return e=new P1({props:{slot:"iconLeft"}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p:D,i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function z1(r){let e,t;return e=new we({props:{loading:r[0],variant:"soft","data-testid":"sign-out-button",$$slots:{iconLeft:[O1],default:[L1]},$$scope:{ctx:r}}}),e.$on("click",r[1]),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,[s]){const o={};1&s&&(o.loading=n[0]),8&s&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function F1(r,e,t){let{onSignOut:n}=e,s=!1;return r.$$set=o=>{"onSignOut"in o&&t(2,n=o.onSignOut)},[s,function(){n(),t(0,s=!0)},n]}class Z1 extends ae{constructor(e){super(),le(this,e,F1,z1,oe,{onSignOut:2})}}const U1="mcpServerModel";function D1(r){let e,t;return e=new xp({props:{tools:r[5],onAuthenticate:r[20],onRevokeAccess:r[21],onToolApprovalConfigChange:r[24],onMCPServerAdd:r[27],onMCPServerSave:r[28],onMCPServerDelete:r[29],onMCPServerToggleDisable:r[30],onMCPServerJSONImport:r[31],isMCPEnabled:r[6]&&r[2].mcpServerList,isMCPImportEnabled:r[6]&&r[2].mcpServerImport,supportedShells:r[7].supportedShells,selectedShell:r[7].selectedShell,startupScript:r[7].startupScript,onShellSelect:r[22],onStartupScriptChange:r[23],isTerminalEnabled:r[2].terminal,isSoundCategoryEnabled:!0,isAgentCategoryEnabled:r[6],isSwarmModeFeatureFlagEnabled:r[8],hasEverUsedRemoteAgent:r[9]}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,s){const o={};32&s[0]&&(o.tools=n[5]),68&s[0]&&(o.isMCPEnabled=n[6]&&n[2].mcpServerList),68&s[0]&&(o.isMCPImportEnabled=n[6]&&n[2].mcpServerImport),128&s[0]&&(o.supportedShells=n[7].supportedShells),128&s[0]&&(o.selectedShell=n[7].selectedShell),128&s[0]&&(o.startupScript=n[7].startupScript),4&s[0]&&(o.isTerminalEnabled=n[2].terminal),64&s[0]&&(o.isAgentCategoryEnabled=n[6]),256&s[0]&&(o.isSwarmModeFeatureFlagEnabled=n[8]),512&s[0]&&(o.hasEverUsedRemoteAgent=n[9]),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function j1(r){let e,t;return e=new Z1({props:{onSignOut:r[25]}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p:D,i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function V1(r){let e,t,n,s;const o=[G1,B1],i=[];function l(a,c){return a[2].rules?0:1}return e=l(r),t=i[e]=o[e](r),{c(){t.c(),n=ve()},m(a,c){i[e].m(a,c),y(a,n,c),s=!0},p(a,c){let d=e;e=l(a),e===d?i[e].p(a,c):(j(),g(i[d],1,1,()=>{i[d]=null}),V(),t=i[e],t?t.p(a,c):(t=i[e]=o[e](a),t.c()),m(t,1),t.m(n.parentNode,n))},i(a){s||(m(t),s=!0)},o(a){g(t),s=!1},d(a){a&&v(n),i[e].d(a)}}}function q1(r){let e,t;return e=new _l({}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p:D,i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function H1(r){return{c:D,m:D,p:D,i:D,o:D,d:D}}function B1(r){let e,t;return e=new Jo({props:{userGuidelines:r[4],userGuidelinesLengthLimit:r[3],updateUserGuideline:r[19]}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,s){const o={};16&s[0]&&(o.userGuidelines=n[4]),8&s[0]&&(o.userGuidelinesLengthLimit=n[3]),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function G1(r){let e,t;return e=new E1({props:{userGuidelines:r[4],userGuidelinesLengthLimit:r[3],updateUserGuideline:r[19]}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,s){const o={};16&s[0]&&(o.userGuidelines=n[4]),8&s[0]&&(o.userGuidelinesLengthLimit=n[3]),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function J1(r){let e,t,n,s,o;const i=[H1,q1,V1,j1,D1],l=[];function a(c,d){var u,f,p;return 256&d[1]&&(t=null),t==null&&(t=!jo(c[39])),t?0:((u=c[39])==null?void 0:u.id)==="context"?1:((f=c[39])==null?void 0:f.id)==="guidelines"?2:((p=c[39])==null?void 0:p.id)==="account"?3:4}return n=a(r,[-1,-1]),s=l[n]=i[n](r),{c(){e=k("span"),s.c(),w(e,"slot","content")},m(c,d){y(c,e,d),l[n].m(e,null),o=!0},p(c,d){let u=n;n=a(c,d),n===u?l[n].p(c,d):(j(),g(l[u],1,1,()=>{l[u]=null}),V(),s=l[n],s?s.p(c,d):(s=l[n]=i[n](c),s.c()),m(s,1),s.m(e,null))},i(c){o||(m(s),o=!0)},o(c){g(s),o=!1},d(c){c&&v(e),l[n].d()}}}function W1(r){let e,t;return e=new Yl({props:{items:r[1],mode:"tree",class:"c-settings-navigation",selectedId:r[0],$$slots:{content:[J1,({item:n})=>({39:n}),({item:n})=>[0,n?256:0]]},$$scope:{ctx:r}}}),{c(){S(e.$$.fragment)},m(n,s){x(e,n,s),t=!0},p(n,s){const o={};2&s[0]&&(o.items=n[1]),1&s[0]&&(o.selectedId=n[0]),1020&s[0]|768&s[1]&&(o.$$scope={dirty:s,ctx:n}),e.$set(o)},i(n){t||(m(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function K1(r){let e,t,n,s;return e=new Ei.Root({props:{$$slots:{default:[W1]},$$scope:{ctx:r}}}),{c(){S(e.$$.fragment)},m(o,i){x(e,o,i),t=!0,n||(s=Ie(window,"message",r[11].onMessageFromExtension),n=!0)},p(o,i){const l={};1023&i[0]|512&i[1]&&(l.$$scope={dirty:i,ctx:o}),e.$set(l)},i(o){t||(m(e.$$.fragment,o),t=!0)},o(o){g(e.$$.fragment,o),t=!1},d(o){C(e,o),n=!1,s()}}}function Y1(r,e,t){let n,s,o,i,l,a,c,d,u,f;const p=new ws(Ne),h=new Ga(Ne),$=new Ja(Ne),b=new go(Ne),E=new vi,I=new Ti(Ne,b,E),A=new Kn(b),M=new Ms(b);Dt(Kn.key,A),Dt(Ms.key,M),Dt(ws.key,p),function(F){Dt(Bo,F)}(I),function(F){Dt(U1,F)}(h);const T=p.getSettingsComponentSupported();Pe(r,T,F=>t(2,i=F));const O=p.getEnableAgentMode();Pe(r,O,F=>t(6,c=F));const G=p.getEnableAgentSwarmMode();Pe(r,G,F=>t(8,u=F));const ne=p.getHasEverUsedRemoteAgent();Pe(r,ne,F=>t(9,f=F)),b.registerConsumer(p),b.registerConsumer(h),b.registerConsumer($);const z=$.getTerminalSettings();let Z;Pe(r,z,F=>t(7,d=F));const P={handleMessageFromExtension:F=>!(!F.data||F.data.type!==ue.navigateToSettingsSection)&&(F.data.data&&typeof F.data.data=="string"&&t(0,Z=F.data.data),!0)};b.registerConsumer(P);const H=p.getDisplayableTools();Pe(r,H,F=>t(5,a=F));const ie=p.getGuidelines();return Pe(r,ie,F=>t(26,l=F)),po(()=>{p.dispose(),A.dispose(),M.dispose()}),p.notifyLoaded(),Ne.postMessage({type:ue.getOrientationStatus}),Ne.postMessage({type:ue.settingsPanelLoaded}),r.$$.update=()=>{var F,J,U;********&r.$$.dirty[0]&&t(4,n=(F=l.userGuidelines)==null?void 0:F.contents),********&r.$$.dirty[0]&&t(3,s=(J=l.userGuidelines)==null?void 0:J.lengthLimit),4&r.$$.dirty[0]&&t(1,o=[i.remoteTools?mn("Tools","",Ql,"section-tools"):void 0,i.userGuidelines&&!i.rules?mn("User Guidelines","Guidelines for Augment Chat to follow.",bp,"guidelines"):void 0,i.rules?mn("Rules and User Guidelines","",Ap,"guidelines"):void 0,i.workspaceContext?{name:"Context",description:"",icon:tc,id:"context"}:void 0,mn("Account","Manage your Augment account settings.",hi,"account")].filter(Boolean)),3&r.$$.dirty[0]&&o.length>1&&!Z&&t(0,Z=(U=o[0])==null?void 0:U.id)},[Z,o,i,s,n,a,c,d,u,f,h,b,T,O,G,ne,z,H,ie,function(F){const J=F.trim();return!(s&&J.length>s)&&(p.updateLocalUserGuidelines(J),Ne.postMessage({type:ue.updateUserGuidelines,data:F}),!0)},function(F){Ne.postMessage({type:ue.toolConfigStartOAuth,data:{authUrl:F}}),p.startPolling()},async function(F){await I.openConfirmationModal({title:"Revoke Access",message:`Are you sure you want to revoke access for ${F.displayName}? This will disconnect the tool and you'll need to reconnect it to use it again.`,confirmButtonText:"Revoke Access",cancelButtonText:"Cancel"})&&Ne.postMessage({type:ue.toolConfigRevokeAccess,data:{toolId:F.identifier}})},function(F){$.updateSelectedShell(F)},function(F){$.updateStartupScript(F)},function(F,J){Ne.postMessage({type:ue.toolApprovalConfigSetRequest,data:{toolId:F,approvalConfig:J}})},function(){Ne.postMessage({type:ue.signOut})},l,F=>h.addServer(F),F=>h.updateServer(F),F=>h.deleteServer(F),F=>h.toggleDisabledServer(F),F=>h.importServersFromJSON(F)]}class X1 extends ae{constructor(e){super(),le(this,e,Y1,K1,oe,{},null,[-1,-1])}}(async function(){Ne&&Ne.initialize&&await Ne.initialize(),new X1({target:document.getElementById("app")})})();
