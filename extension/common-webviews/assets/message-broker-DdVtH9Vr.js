var E=Object.defineProperty;var A=(T,I,s)=>I in T?E(T,I,{enumerable:!0,configurable:!0,writable:!0,value:s}):T[I]=s;var N=(T,I,s)=>A(T,typeof I!="symbol"?I+"":I,s);import{W as S}from"./IconButtonAugment-CQzh_Hae.js";import{A as O}from"./async-messaging-D4p6YcQf.js";var e=(T=>(T[T.TEXT=0]="TEXT",T[T.TOOL_RESULT=1]="TOOL_RESULT",T[T.IMAGE=2]="IMAGE",T[T.IMAGE_ID=3]="IMAGE_ID",T[T.IDE_STATE=4]="IDE_STATE",T[T.EDIT_EVENTS=5]="EDIT_EVENTS",T[T.CHECKPOINT_REF=6]="CHECKPOINT_REF",T[T.CHANGE_PERSONALITY=7]="CHANGE_PERSONALITY",T[T.FILE=8]="FILE",T[T.FILE_ID=9]="FILE_ID",T))(e||{}),R=(T=>(T[T.IMAGE_FORMAT_UNSPECIFIED=0]="IMAGE_FORMAT_UNSPECIFIED",T[T.PNG=1]="PNG",T[T.JPEG=2]="JPEG",T[T.GIF=3]="GIF",T[T.WEBP=4]="WEBP",T))(R||{}),o=(T=>(T[T.UNSPECIFIED=0]="UNSPECIFIED",T[T.USER_EDIT=1]="USER_EDIT",T[T.CHECKPOINT_REVERT=2]="CHECKPOINT_REVERT",T))(o||{}),t=(T=>(T[T.CONTENT_TYPE_UNSPECIFIED=0]="CONTENT_TYPE_UNSPECIFIED",T[T.CONTENT_TEXT=1]="CONTENT_TEXT",T[T.CONTENT_IMAGE=2]="CONTENT_IMAGE",T))(t||{}),M=(T=>(T[T.RAW_RESPONSE=0]="RAW_RESPONSE",T[T.SUGGESTED_QUESTIONS=1]="SUGGESTED_QUESTIONS",T[T.MAIN_TEXT_FINISHED=2]="MAIN_TEXT_FINISHED",T[T.TOOL_USE=5]="TOOL_USE",T[T.AGENT_MEMORY=6]="AGENT_MEMORY",T[T.TOOL_USE_START=7]="TOOL_USE_START",T[T.THINKING=8]="THINKING",T))(M||{}),C=(T=>(T.chat="CHAT",T.agent="AGENT",T.remoteAgent="REMOTE_AGENT",T.memories="MEMORIES",T.orientation="ORIENTATION",T.memoriesCompression="MEMORIES_COMPRESSION",T.cliAgent="CLI_AGENT",T))(C||{}),a=(T=>(T[T.DEFAULT=0]="DEFAULT",T[T.PROTOTYPER=1]="PROTOTYPER",T[T.BRAINSTORM=2]="BRAINSTORM",T[T.REVIEWER=3]="REVIEWER",T))(a||{}),G=(T=>(T[T.ALWAYS_ATTACHED=0]="ALWAYS_ATTACHED",T[T.MANUAL=1]="MANUAL",T[T.AGENT_REQUESTED=2]="AGENT_REQUESTED",T))(G||{});class D extends O{constructor(s){super(_=>{this._host.postMessage(_)});N(this,"_consumers",[]);this._host=s,this.onMessageFromExtension=this.onMessageFromExtension.bind(this)}dispose(){this._consumers=[]}postMessage(s){this._host.postMessage(s)}registerConsumer(s){this._consumers.push(s)}onMessageFromExtension(s){s.data.type!==S.asyncWrapper&&this._consumers.forEach(_=>{_.handleMessageFromExtension(s)})}}export{M as C,o as E,R as I,D as M,a as P,G as R,e as a,t as b,C as c};
