import{p as v,q as d,r as e,t as p,u as m,v as x,w as g,x as w}from"./SpinnerAugment-uKUHz-bK.js";function C(r,c){const u=c.token={};function n(o,t,k,b){if(c.token!==u)return;c.resolved=b;let s=c.ctx;k!==void 0&&(s=s.slice(),s[k]=b);const l=o&&(c.current=o)(s);let f=!1;c.block&&(c.blocks?c.blocks.forEach((h,i)=>{i!==t&&h&&(p(),m(h,1,1,()=>{c.blocks[i]===h&&(c.blocks[i]=null)}),x())}):c.block.d(1),l.c(),g(l,1),l.m(c.mount(),c.anchor),f=!0),c.block=l,c.blocks&&(c.blocks[t]=l),f&&w()}if(v(r)){const o=d();if(r.then(t=>{e(o),n(c.then,1,c.value,t),e(null)},t=>{if(e(o),n(c.catch,2,c.error,t),e(null),!c.hasCatch)throw t}),c.current!==c.pending)return n(c.pending,0),!0}else{if(c.current!==c.then)return n(c.then,1,c.value,r),!0;c.resolved=r}}function E(r,c,u){const n=c.slice(),{resolved:o}=r;r.current===r.then&&(n[r.value]=o),r.current===r.catch&&(n[r.error]=o),r.block.p(n,u)}export{C as h,E as u};
